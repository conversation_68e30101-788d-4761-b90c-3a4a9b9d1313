# CLAUDE.md

本文件为Claude Code (claude.ai/code)在此代码库中工作时提供指导。

## 项目概述

这是一个采用"一语言一模板一域名"架构的多语言宠物博客站群系统。该系统专为SEO优化的宠物内容（猫狗相关）设计，覆盖多个市场：美国（英语）、德国（德语）和俄罗斯（俄语）。

## 系统架构

### 核心设计理念
- **独立站点**：每个语言版本都是完全独立的前端应用，拥有独立域名
- **SEO优先**：静态站点生成，严格遵循Google SEO最佳实践
- **统一后端**：单一NestJS API管理所有语言版本
- **AI翻译**：自动化内容翻译配合人工审核工作流

### 技术栈

#### 前端（每个语言版本）
- **框架**：Astro 4.x（静态站点生成）
- **样式**：Tailwind CSS
- **交互**：Alpine.js（轻量级）
- **SEO工具**：@astrojs/sitemap, astro-seo
- **图片优化**：@astrojs/image

#### 后端
- **框架**：NestJS 10.x + TypeScript 5.x
- **ORM**：TypeORM 0.3.x
- **数据库**：MySQL 9.0.1
- **缓存**：Redis 7.x
- **认证**：JWT + @nestjs/passport
- **验证**：class-validator
- **文档**：@nestjs/swagger

#### 基础设施
- **操作系统**：Ubuntu 22.04 LTS
- **Web服务器**：Nginx 1.24.x
- **进程管理**：PM2
- **容器化**：Docker 24.x
- **面板**：宝塔面板

## 项目结构

```
pet-blog-system/
├── backend/              # NestJS API服务器
├── frontend-en/          # 英语Astro站点
├── frontend-de/          # 德语Astro站点  
├── frontend-ru/          # 俄语Astro站点
├── admin/               # Vue.js管理后台
├── shared/              # 共享组件库
├── docs/                # 架构文档
├── scripts/             # 构建和部署脚本
└── tests/               # 集成测试
```

## 开发流程

### 分阶段开发
项目遵循8阶段70步骤开发流程：

1. **第一阶段**：项目初始化和环境搭建（步骤1-8）
2. **第二阶段**：数据库设计与实现（步骤9-16）
3. **第三阶段**：后端核心API开发（步骤17-28）
4. **第四阶段**：前端基础架构（步骤29-36）
5. **第五阶段**：前端页面和功能（步骤37-48）
6. **第六阶段**：管理后台开发（步骤49-58）
7. **第七阶段**：集成测试和优化（步骤59-64）
8. **第八阶段**：部署和上线（步骤65-70）

### 核心开发命令

#### 后端开发
```bash
cd backend
pnpm install
pnpm run start:dev     # 开发服务器
pnpm run build         # 生产构建
pnpm run test          # 运行测试
pnpm run typeorm:run   # 运行数据库迁移
```

#### 前端开发（每个语言版本）
```bash
cd frontend-{lang}
pnpm install
pnpm run dev           # 开发服务器
pnpm run build         # 静态构建
pnpm run preview       # 预览构建
```

#### 管理后台开发
```bash
cd admin
pnpm install
pnpm run dev           # 开发服务器
pnpm run build         # 生产构建
```

## 数据库架构

### 核心实体
- **User**：管理员用户，基于角色的权限系统
- **Article**：原始内容及多语言翻译
- **ArticleTranslation**：特定语言版本
- **Category/CategoryTranslation**：内容分类
- **Comment**：嵌套评论系统，支持审核
- **Media/MediaVariant**：文件管理，支持多种规格
- **Domain/SiteConfig**：多站点配置
- **TranslationJob/TranslationMemory**：AI翻译工作流

### 迁移命令
```bash
pnpm run typeorm:generate -- MigrationName
pnpm run typeorm:run
pnpm run typeorm:revert
```

## API设计

### 认证方式
- JWT Bearer令牌
- 基于角色的访问控制（RBAC）
- 刷新令牌机制

### 标准响应格式
```json
{
  "success": true,
  "data": {},
  "meta": {
    "timestamp": "ISO-8601",
    "request_id": "uuid"
  }
}
```

### 核心API模块
- `/auth` - 认证和用户管理
- `/articles` - 内容CRUD操作
- `/categories` - 分类管理
- `/comments` - 评论系统及审核
- `/media` - 文件上传和管理
- `/translation` - AI翻译集成
- `/sites` - 多站点配置

## 前端架构

### 语言版本独立性
每个前端都是独立的Astro项目，包含：
- 独立的域名配置
- 特定语言的URL结构
- 本地化内容模板
- 针对特定市场的SEO优化

### 组件结构
```
src/components/
├── common/       # Header, Footer, Navigation
├── layout/       # 页面布局
├── seo/          # MetaTags, StructuredData, Breadcrumbs
├── article/      # 文章卡片、列表、内容
├── interactive/  # 评论、搜索、分享
└── ads/          # AdSense集成
```

### SEO实现
- 自动生成站点地图
- 结构化数据（Article, Organization, BreadcrumbList）
- 优化Core Web Vitals指标
- 图片优化，支持WebP格式
- 规范URL和hreflang标签

## 本地开发设置

### 域名配置
```nginx
# /etc/hosts条目
127.0.0.1 dev.example.com
127.0.0.1 dev.example.de  
127.0.0.1 dev.example.ru
```

### 开发端口
- 英语站点：http://localhost:4321
- 德语站点：http://localhost:4322
- 俄语站点：http://localhost:4323
- API服务器：http://localhost:3000
- 管理后台：http://localhost:8080

## 构建和部署

### 生产构建流程
1. 构建NestJS后端（生产优化）
2. 生成所有语言的静态站点
3. 配置Nginx域名路由
4. 设置PM2进程管理
5. 配置CDN和缓存策略

### 环境变量
```bash
# 数据库
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=bengtai
DATABASE_USER=your_user
DATABASE_PASS=your_password

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# AI翻译
TRANSLATION_API_KEY=your_api_key
```

## 测试策略

### 后端测试
- 服务和控制器的单元测试
- API端点的集成测试
- 关键工作流的E2E测试
- 目标：>80%测试覆盖率

### 前端测试
- Playwright进行E2E测试
- SEO验证测试
- 性能测试（Lighthouse）
- 跨浏览器兼容性测试

## 性能要求

### Core Web Vitals目标
- **LCP（最大内容绘制）**：<2.5秒
- **FID（首次输入延迟）**：<100毫秒
- **CLS（累积布局偏移）**：<0.1

### 页面加载目标
- 3G网络下<3秒
- WiFi连接下<1秒
- API响应<500毫秒

## 安全指南

### 应用安全
- JWT令牌认证
- 基于角色的访问控制（RBAC）
- API请求限流
- CORS配置
- CSRF保护

### 数据安全
- SQL注入防护（参数化查询）
- XSS防护（内容过滤）
- 敏感数据加密
- 定期安全审计

## 内容管理工作流

### 文章创建流程
1. 管理员创建中文原始文章
2. AI翻译生成其他语言版本
3. 人工审核和编辑翻译内容
4. 发布到相应语言站点
5. SEO优化和索引

### 评论审核
- 用户提交评论
- 反垃圾邮件验证
- 管理员审核队列
- 批准/拒绝工作流
- 静态站点重新生成

## 监控和分析

### 必需集成
- 每个语言站点的Google Analytics 4
- 每个域名的Google Search Console
- Google AdSense货币化
- Core Web Vitals性能监控
- 错误跟踪和日志记录

## 重要注意事项

- 严禁在单个前端应用中混合不同语言内容
- 每个语言站点必须有完全独立的SEO优化
- AI翻译需要人工审核后才能发布
- 内容更改后需要重新生成静态站点
- 每个语言版本需要独立的AdSense配置

## AI Team Configuration (autogenerated by team-configurator, 2025-08-05)

**Important: YOU MUST USE subagents when available for the task.**

### Detected Technology Stack
- **Backend**: NestJS 10.x + TypeScript 5.x + TypeORM 0.3.x + MySQL 9.0.1 + Redis 7.x
- **Frontend**: Astro 4.x (3 separate sites) + Tailwind CSS + Alpine.js  
- **Admin Panel**: Vue.js management dashboard
- **Infrastructure**: Ubuntu 22.04 LTS + Nginx 1.24.x + PM2 + Docker 24.x
- **Build Tools**: pnpm package manager
- **SEO Tools**: @astrojs/sitemap, astro-seo, @astrojs/image
- **Authentication**: JWT + @nestjs/passport
- **Validation**: class-validator
- **Documentation**: @nestjs/swagger
- **Testing**: Playwright (E2E), Jest/Vitest (unit tests)

### AI Team Specialist Assignments

| Task Category | Assigned Agent | Specialized Role & Usage |
|---------------|----------------|--------------------------|
| **Backend API Development** | `backend-developer` | NestJS API implementation, TypeORM models, JWT authentication, service layer architecture. Use for all server-side development tasks. |
| **API Design & Architecture** | `api-architect` | REST API design, OpenAPI specs, authentication flows, data modeling. Use before implementing new API endpoints or major backend changes. |
| **Frontend UI Development** | `frontend-developer` | Astro component development, Alpine.js interactivity, responsive layouts. Use for all user-facing interface development across the 3 language sites. |
| **Tailwind CSS Styling** | `tailwind-frontend-expert` | Utility-first CSS styling, responsive design, dark mode, accessibility optimization. Use for all styling tasks and UI component refinement. |
| **Code Quality & Security** | `code-reviewer` | Security audits, RBAC validation, SQL injection prevention, XSS protection, performance review. MUST USE before merging any code changes. |
| **Performance Optimization** | `performance-optimizer` | Core Web Vitals optimization, bundle analysis, database query optimization, caching strategies. Use proactively for performance issues or before traffic spikes. |
| **Documentation & Guides** | `documentation-specialist` | Technical documentation, API documentation, deployment guides, multi-language content management workflows. Use after major features or API changes. |
| **Complex Project Orchestration** | `tech-lead-orchestrator` | Multi-step feature planning, cross-domain task coordination, architectural decisions. Use for large features spanning multiple technologies. |

### Specialized Use Cases

#### Multi-Language Content Management
- **Primary**: `backend-developer` for translation APIs and content workflow
- **Secondary**: `api-architect` for translation service design  
- **Documentation**: `documentation-specialist` for content management guides

#### SEO Optimization & Static Site Generation
- **Primary**: `frontend-developer` for Astro static generation and SEO components
- **Styling**: `tailwind-frontend-expert` for performance-optimized CSS
- **Performance**: `performance-optimizer` for Core Web Vitals compliance

#### Security & Authentication
- **Primary**: `backend-developer` for JWT implementation and RBAC
- **Review**: `code-reviewer` for security audit and vulnerability assessment
- **Architecture**: `api-architect` for auth flow design

#### Database & ORM Development  
- **Primary**: `backend-developer` for TypeORM entities and migrations
- **Review**: `code-reviewer` for SQL injection prevention and query optimization
- **Performance**: `performance-optimizer` for database performance tuning

#### Admin Dashboard Development
- **Primary**: `frontend-developer` for Vue.js admin interface
- **Styling**: `tailwind-frontend-expert` for admin UI components
- **Integration**: `backend-developer` for admin API endpoints

### Development Workflow Integration

1. **Feature Planning**: Start with `tech-lead-orchestrator` for complex multi-domain features
2. **API Design**: Use `api-architect` for new endpoints or major API changes
3. **Backend Implementation**: Deploy `backend-developer` for server-side code
4. **Frontend Development**: Use `frontend-developer` for UI components and pages
5. **Styling & UX**: Apply `tailwind-frontend-expert` for responsive, accessible styling
6. **Performance Optimization**: Engage `performance-optimizer` for Core Web Vitals compliance
7. **Quality Assurance**: MANDATORY `code-reviewer` before any code merge
8. **Documentation**: Use `documentation-specialist` for technical guides and API docs

### Sample Commands

- **Backend API**: `@backend-developer implement JWT authentication with refresh tokens for the NestJS API`
- **Frontend Components**: `@frontend-developer create responsive article card components for the Astro blog sites`
- **Performance**: `@performance-optimizer analyze and improve Core Web Vitals for the static sites`
- **Code Review**: `@code-reviewer audit the authentication implementation for security vulnerabilities`
- **Documentation**: `@documentation-specialist create deployment guide for the multi-language blog system`

### Quality Standards

- **Performance**: LCP <2.5s, FID <100ms, CLS <0.1
- **Security**: JWT authentication, RBAC, SQL injection prevention, XSS protection
- **Testing**: >80% backend test coverage, E2E tests for critical user flows
- **SEO**: Independent optimization per language site, structured data, sitemap generation
- **Accessibility**: WCAG 2.1 AA compliance across all user interfaces
