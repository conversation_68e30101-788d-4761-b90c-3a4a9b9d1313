# 多语言宠物博客站群系统 - API接口设计文档

## 1. API设计概述

### 1.1 设计原则
- **RESTful规范**：遵循REST架构风格，使用标准HTTP方法
- **版本控制**：URL路径包含版本号，便于API升级
- **统一响应格式**：所有接口返回统一的JSON格式
- **错误处理规范**：使用标准HTTP状态码和自定义错误码
- **安全性设计**：JWT认证、请求限流、数据验证

### 1.2 基础信息
- **Base URL**: `https://api.example.com/api/v1`
- **认证方式**: JWT Bearer Token
- **请求格式**: JSON
- **响应格式**: JSON
- **字符编码**: UTF-8

### 1.3 通用请求头
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer {token}
X-Language: en|de|ru|zh
X-Request-ID: {uuid}
```

### 1.4 统一响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "meta": {
    "timestamp": "2024-01-20T10:00:00Z",
    "request_id": "uuid-string"
  }
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      // 详细错误信息
    }
  },
  "meta": {
    "timestamp": "2024-01-20T10:00:00Z",
    "request_id": "uuid-string"
  }
}
```

#### 分页响应
```json
{
  "success": true,
  "data": [
    // 数据列表
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  },
  "meta": {
    "timestamp": "2024-01-20T10:00:00Z",
    "request_id": "uuid-string"
  }
}
```

## 2. 认证相关接口

### 2.1 用户登录
**POST** `/auth/login`

#### 请求参数
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "super_admin"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
      "expires_in": 3600
    }
  }
}
```

### 2.2 刷新Token
**POST** `/auth/refresh`

#### 请求参数
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 2.3 用户登出
**POST** `/auth/logout`

需要认证

### 2.4 获取当前用户信息
**GET** `/auth/me`

需要认证

## 3. 文章管理接口

### 3.1 获取文章列表（公开）
**GET** `/articles`

#### 查询参数
- `language`: 语言代码 (必需)
- `category_id`: 分类ID
- `tag`: 标签名
- `search`: 搜索关键词
- `status`: 状态筛选（仅管理员）
- `page`: 页码，默认1
- `per_page`: 每页数量，默认20
- `sort`: 排序字段 (published_at|view_count|comment_count)
- `order`: 排序方向 (asc|desc)

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "slug": "how-to-care-for-your-cat",
      "title": "How to Care for Your Cat",
      "excerpt": "Learn the basics of cat care...",
      "featured_image": "https://example.com/images/cat-care.jpg",
      "category": {
        "id": 1,
        "name": "Cat Care",
        "slug": "cat-care"
      },
      "author": {
        "id": 1,
        "username": "admin",
        "avatar": "https://example.com/avatars/admin.jpg"
      },
      "view_count": 1234,
      "comment_count": 56,
      "published_at": "2024-01-15T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

### 3.2 获取文章详情（公开）
**GET** `/articles/{slug}`

#### 路径参数
- `slug`: 文章URL标识

#### 查询参数
- `language`: 语言代码 (必需)

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "slug": "how-to-care-for-your-cat",
    "title": "How to Care for Your Cat",
    "content": "<h2>Introduction</h2><p>Cat care is...</p>",
    "excerpt": "Learn the basics of cat care...",
    "featured_image": "https://example.com/images/cat-care.jpg",
    "meta_title": "Cat Care Guide - Pet Blog",
    "meta_description": "Complete guide on how to care for your cat...",
    "meta_keywords": "cat care, pet care, feline health",
    "category": {
      "id": 1,
      "name": "Cat Care",
      "slug": "cat-care"
    },
    "tags": ["cat", "care", "health"],
    "author": {
      "id": 1,
      "username": "admin",
      "avatar": "https://example.com/avatars/admin.jpg"
    },
    "view_count": 1234,
    "comment_count": 56,
    "published_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-16T10:00:00Z",
    "structured_data": {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": "How to Care for Your Cat",
      "author": {
        "@type": "Person",
        "name": "admin"
      }
    }
  }
}
```

### 3.3 创建文章（管理员）
**POST** `/admin/articles`

需要认证

#### 请求参数
```json
{
  "category_id": 1,
  "slug": "how-to-care-for-your-cat",
  "status": "draft",
  "featured_image": "image-uuid",
  "translations": {
    "zh": {
      "title": "如何照顾你的猫",
      "content": "<h2>介绍</h2><p>猫的护理是...</p>",
      "excerpt": "学习猫护理的基础知识...",
      "meta_title": "猫护理指南 - 宠物博客",
      "meta_description": "关于如何照顾你的猫的完整指南...",
      "meta_keywords": "猫护理, 宠物护理, 猫健康"
    }
  },
  "tags": ["cat", "care", "health"]
}
```

### 3.4 更新文章（管理员）
**PUT** `/admin/articles/{id}`

需要认证

### 3.5 删除文章（管理员）
**DELETE** `/admin/articles/{id}`

需要认证

### 3.6 批量操作文章（管理员）
**POST** `/admin/articles/batch`

需要认证

#### 请求参数
```json
{
  "action": "publish|archive|delete",
  "article_ids": [1, 2, 3]
}
```

## 4. 文章翻译接口

### 4.1 创建翻译任务
**POST** `/admin/translations/jobs`

需要认证

#### 请求参数
```json
{
  "article_id": 1,
  "source_language": "zh",
  "target_languages": ["en", "de", "ru"]
}
```

### 4.2 获取翻译任务状态
**GET** `/admin/translations/jobs/{id}`

需要认证

### 4.3 获取翻译任务列表
**GET** `/admin/translations/jobs`

需要认证

### 4.4 AI翻译接口（内部）
**POST** `/admin/translations/ai-translate`

需要认证

#### 请求参数
```json
{
  "text": "需要翻译的文本",
  "source_language": "zh",
  "target_language": "en",
  "context": "article_title|article_content|meta_description"
}
```

### 4.5 保存翻译结果
**PUT** `/admin/articles/{id}/translations/{language}`

需要认证

#### 请求参数
```json
{
  "title": "Translated Title",
  "content": "<p>Translated content...</p>",
  "excerpt": "Translated excerpt...",
  "meta_title": "Translated meta title",
  "meta_description": "Translated meta description",
  "meta_keywords": "translated, keywords",
  "translation_status": "translated|reviewed|published"
}
```

## 5. 分类管理接口

### 5.1 获取分类列表（公开）
**GET** `/categories`

#### 查询参数
- `language`: 语言代码 (必需)
- `type`: 类型筛选 (cat|dog)
- `parent_id`: 父分类ID

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Cat Care",
      "slug": "cat-care",
      "type": "cat",
      "description": "Everything about cat care",
      "parent_id": null,
      "children": [
        {
          "id": 5,
          "name": "Feeding",
          "slug": "cat-feeding",
          "type": "cat",
          "parent_id": 1
        }
      ],
      "article_count": 25
    }
  ]
}
```

### 5.2 获取分类详情（公开）
**GET** `/categories/{slug}`

### 5.3 创建分类（管理员）
**POST** `/admin/categories`

需要认证

### 5.4 更新分类（管理员）
**PUT** `/admin/categories/{id}`

需要认证

### 5.5 删除分类（管理员）
**DELETE** `/admin/categories/{id}`

需要认证

## 6. 评论管理接口

### 6.1 获取文章评论（公开）
**GET** `/articles/{article_id}/comments`

#### 查询参数
- `page`: 页码
- `per_page`: 每页数量
- `sort`: 排序 (created_at|likes)

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "author_name": "John Doe",
      "content": "Great article! Very helpful.",
      "created_at": "2024-01-15T10:00:00Z",
      "is_admin_reply": false,
      "replies": [
        {
          "id": 2,
          "author_name": "Admin",
          "content": "Thank you for your feedback!",
          "created_at": "2024-01-15T11:00:00Z",
          "is_admin_reply": true
        }
      ]
    }
  ]
}
```

### 6.2 提交评论（公开）
**POST** `/articles/{article_id}/comments`

#### 请求参数
```json
{
  "author_name": "John Doe",
  "author_email": "<EMAIL>",
  "content": "This is a great article!",
  "parent_id": null,
  "language": "en"
}
```

### 6.3 获取待审核评论（管理员）
**GET** `/admin/comments/pending`

需要认证

### 6.4 审核评论（管理员）
**PUT** `/admin/comments/{id}/status`

需要认证

#### 请求参数
```json
{
  "status": "approved|spam|trash"
}
```

### 6.5 回复评论（管理员）
**POST** `/admin/comments/{id}/reply`

需要认证

### 6.6 批量操作评论（管理员）
**POST** `/admin/comments/batch`

需要认证

## 7. 媒体管理接口

### 7.1 上传文件
**POST** `/admin/media/upload`

需要认证，支持multipart/form-data

#### 请求参数
- `file`: 文件对象
- `type`: 文件类型 (image|video|document)
- `alt_text`: 替代文本（图片）

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "filename": "cat-care-guide.jpg",
    "url": "https://example.com/uploads/2024/01/cat-care-guide.jpg",
    "type": "image",
    "size": 102400,
    "width": 1200,
    "height": 800,
    "variants": {
      "thumbnail": "https://example.com/uploads/2024/01/cat-care-guide-thumb.jpg",
      "medium": "https://example.com/uploads/2024/01/cat-care-guide-medium.jpg",
      "large": "https://example.com/uploads/2024/01/cat-care-guide-large.jpg"
    }
  }
}
```

### 7.2 获取媒体列表
**GET** `/admin/media`

需要认证

#### 查询参数
- `type`: 类型筛选
- `page`: 页码
- `per_page`: 每页数量

### 7.3 删除媒体文件
**DELETE** `/admin/media/{id}`

需要认证

## 8. 站点配置接口

### 8.1 获取站点配置（公开）
**GET** `/site/config`

#### 查询参数
- `language`: 语言代码 (必需)

#### 响应示例
```json
{
  "success": true,
  "data": {
    "site_name": "Pet Blog",
    "site_description": "Your trusted source for pet care",
    "site_logo": "https://example.com/logo.png",
    "social_links": {
      "facebook": "https://facebook.com/petblog",
      "twitter": "https://twitter.com/petblog",
      "instagram": "https://instagram.com/petblog"
    },
    "contact_email": "<EMAIL>",
    "google_analytics_id": "UA-XXXXXXXX-X",
    "adsense_client_id": "ca-pub-XXXXXXXX"
  }
}
```

### 8.2 更新站点配置（管理员）
**PUT** `/admin/site/config`

需要认证

#### 请求参数
```json
{
  "language": "en",
  "configs": {
    "site_name": "Pet Blog Updated",
    "site_description": "New description"
  }
}
```

### 8.3 获取域名列表（管理员）
**GET** `/admin/domains`

需要认证

### 8.4 管理域名绑定（管理员）
**POST** `/admin/domains`
**PUT** `/admin/domains/{id}`
**DELETE** `/admin/domains/{id}`

需要认证

## 9. 搜索接口

### 9.1 全站搜索（公开）
**GET** `/search`

#### 查询参数
- `q`: 搜索关键词 (必需)
- `language`: 语言代码 (必需)
- `type`: 搜索类型 (article|category|tag)
- `page`: 页码
- `per_page`: 每页数量

#### 响应示例
```json
{
  "success": true,
  "data": {
    "articles": [
      {
        "id": 1,
        "title": "How to Care for Your Cat",
        "excerpt": "Learn the basics of cat care...",
        "url": "/cat-care/how-to-care-for-your-cat",
        "highlight": "Learn the basics of <mark>cat care</mark>..."
      }
    ],
    "categories": [],
    "tags": []
  },
  "meta": {
    "query": "cat care",
    "total_results": 15,
    "search_time": 0.123
  }
}
```

### 9.2 搜索建议（公开）
**GET** `/search/suggestions`

#### 查询参数
- `q`: 搜索关键词前缀
- `language`: 语言代码

## 10. 统计分析接口

### 10.1 获取文章统计（管理员）
**GET** `/admin/analytics/articles`

需要认证

#### 查询参数
- `start_date`: 开始日期
- `end_date`: 结束日期
- `language`: 语言筛选

### 10.2 获取访问统计（管理员）
**GET** `/admin/analytics/visits`

需要认证

### 10.3 获取热门内容（管理员）
**GET** `/admin/analytics/popular`

需要认证

### 10.4 记录页面浏览（内部）
**POST** `/analytics/pageview`

#### 请求参数
```json
{
  "article_id": 1,
  "language": "en",
  "page_type": "article",
  "page_url": "/cat-care/how-to-care-for-your-cat",
  "referrer": "https://google.com"
}
```

## 11. 系统管理接口

### 11.1 获取系统信息（管理员）
**GET** `/admin/system/info`

需要认证

### 11.2 获取活动日志（管理员）
**GET** `/admin/system/logs`

需要认证

### 11.3 清理缓存（管理员）
**POST** `/admin/system/cache/clear`

需要认证

### 11.4 健康检查（公开）
**GET** `/health`

#### 响应示例
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 86400,
    "services": {
      "database": "healthy",
      "redis": "healthy",
      "storage": "healthy"
    }
  }
}
```

## 12. 错误码定义

### 12.1 通用错误码
- `VALIDATION_ERROR`: 400 - 请求参数验证失败
- `UNAUTHORIZED`: 401 - 未授权
- `FORBIDDEN`: 403 - 禁止访问
- `NOT_FOUND`: 404 - 资源不存在
- `CONFLICT`: 409 - 资源冲突
- `RATE_LIMIT_EXCEEDED`: 429 - 请求频率超限
- `INTERNAL_ERROR`: 500 - 服务器内部错误

### 12.2 业务错误码
- `ARTICLE_NOT_FOUND`: 文章不存在
- `CATEGORY_NOT_FOUND`: 分类不存在
- `TRANSLATION_FAILED`: 翻译失败
- `INVALID_LANGUAGE`: 无效的语言代码
- `DUPLICATE_SLUG`: URL标识重复
- `COMMENT_SPAM`: 评论被识别为垃圾信息
- `UPLOAD_FAILED`: 文件上传失败
- `INVALID_FILE_TYPE`: 无效的文件类型

## 13. API安全措施

### 13.1 认证与授权
- JWT Token有效期：1小时
- Refresh Token有效期：7天
- 角色权限控制（RBAC）
- API密钥管理

### 13.2 请求限流
- 公开接口：60次/分钟
- 认证接口：120次/分钟
- 搜索接口：30次/分钟
- 上传接口：10次/分钟

### 13.3 数据验证
- 输入参数验证
- SQL注入防护
- XSS防护
- CSRF保护

### 13.4 安全头部
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

## 14. API版本管理

### 14.1 版本策略
- 主版本号变更：不兼容的API修改
- 次版本号变更：向下兼容的功能新增
- 修订号变更：向下兼容的问题修正

### 14.2 版本迁移
- 新版本发布后，旧版本维护6个月
- 提供版本迁移指南
- 支持版本协商机制

## 15. API文档工具

### 15.1 Swagger集成
- 访问地址：`/api/docs`
- 支持在线测试
- 自动生成客户端SDK

### 15.2 Postman集合
- 提供完整的API集合
- 包含示例请求
- 环境变量配置

## 16. 总结

本API设计遵循RESTful最佳实践，提供了完整的多语言博客系统所需的所有接口。设计特点：

1. **统一规范**：统一的请求响应格式，便于前端处理
2. **安全可靠**：完善的认证授权和安全措施
3. **高性能**：合理的缓存策略和请求限流
4. **易于扩展**：版本化管理，便于功能迭代
5. **开发友好**：详细的文档和错误提示

通过这套API，可以支持多个独立的前端站点，实现内容的统一管理和分发。