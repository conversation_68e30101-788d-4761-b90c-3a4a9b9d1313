# 多语言宠物博客站群系统 - 部署指南文档

## 1. 部署概述

### 1.1 部署架构
本系统采用前后端分离架构，需要部署以下组件：
- **后端API服务**: NestJS应用
- **前端静态站点**: 3个独立的Astro站点（英语、德语、俄语）
- **管理后台**: Vue.js SPA应用
- **数据库**: MySQL 9.0.1（已提供）
- **缓存服务**: Redis 7.x
- **Web服务器**: Nginx
- **进程管理**: PM2

### 1.2 服务器要求
- **操作系统**: Ubuntu 22.04 LTS 或 CentOS 8+
- **CPU**: 4核心及以上
- **内存**: 8GB及以上
- **硬盘**: 50GB SSD及以上
- **带宽**: 10Mbps及以上
- **软件环境**: Node.js 20.x, Nginx 1.24+, PM2, Git

### 1.3 域名准备
确保以下域名已解析到服务器IP：
- example.com (英语站点)
- example.de (德语站点)
- example.ru (俄语站点)
- api.example.com (API服务)
- admin.example.com (管理后台)

## 2. 服务器环境准备

### 2.1 基础环境安装

#### 2.1.1 更新系统
```bash
# Ubuntu
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl git wget vim build-essential

# CentOS
sudo yum update -y
sudo yum install -y curl git wget vim gcc-c++ make
```

#### 2.1.2 安装Node.js
```bash
# 使用NodeSource仓库安装Node.js 20.x
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# 安装pnpm
npm install -g pnpm

# 验证安装
node --version  # 应显示 v20.x.x
pnpm --version  # 应显示 8.x.x
```

#### 2.1.3 安装Nginx
```bash
# Ubuntu
sudo apt install -y nginx

# CentOS
sudo yum install -y nginx

# 启动并设置开机自启
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 2.1.4 安装Redis
```bash
# Ubuntu
sudo apt install -y redis-server

# CentOS
sudo yum install -y redis

# 配置Redis
sudo vim /etc/redis/redis.conf
# 修改以下配置：
# bind 127.0.0.1
# protected-mode yes
# requirepass your_redis_password

# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis
```

#### 2.1.5 安装PM2
```bash
sudo npm install -g pm2

# 设置PM2开机自启
pm2 startup
# 按照提示执行相应命令
```

### 2.2 宝塔面板安装（可选）

如果使用宝塔面板管理服务器：

```bash
# 宝塔Linux面板安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

安装完成后：
1. 访问宝塔面板地址
2. 安装LNMP环境
3. 配置PHP版本为7.4+
4. 安装Redis扩展
5. 配置防火墙规则

### 2.3 安全配置

#### 2.3.1 配置防火墙
```bash
# 使用ufw (Ubuntu)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 3000/tcp  # API (仅内网)
sudo ufw enable

# 使用firewalld (CentOS)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

#### 2.3.2 创建部署用户
```bash
# 创建deploy用户
sudo adduser deploy
sudo usermod -aG sudo deploy

# 配置SSH密钥登录
su - deploy
mkdir ~/.ssh
chmod 700 ~/.ssh
# 将公钥添加到 authorized_keys
vim ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

## 3. 项目部署

### 3.1 获取项目代码

```bash
# 切换到deploy用户
su - deploy

# 创建项目目录
mkdir -p /home/<USER>/pet-blog-system
cd /home/<USER>/pet-blog-system

# 克隆项目代码
git clone https://github.com/your-repo/pet-blog-system.git .

# 安装依赖
pnpm install
```

### 3.2 后端API部署

#### 3.2.1 配置环境变量
```bash
cd backend
cp .env.example .env.production
vim .env.production
```

配置以下环境变量：
```env
# 应用配置
NODE_ENV=production
PORT=3000
API_PREFIX=/api/v1

# 数据库配置
DB_HOST=************
DB_PORT=3306
DB_USERNAME=bengtai
DB_PASSWORD=weizhen258
DB_DATABASE=bengtai

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=3600
JWT_REFRESH_SECRET=your_refresh_secret_key_here
JWT_REFRESH_EXPIRES_IN=604800

# AI翻译配置
AI_API_URL=https://ai.wanderintree.top
AI_API_KEY=sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
AI_MODEL=gemini-2.5-pro

# 文件上传配置
UPLOAD_DIR=/home/<USER>/pet-blog-system/uploads
MAX_FILE_SIZE=10485760

# CORS配置
CORS_ORIGINS=https://example.com,https://example.de,https://example.ru,https://admin.example.com
```

#### 3.2.2 构建项目
```bash
# 构建生产版本
pnpm build

# 运行数据库迁移
pnpm typeorm migration:run
```

#### 3.2.3 使用PM2启动服务
```bash
# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'pet-blog-api',
    script: 'dist/main.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/home/<USER>/logs/api-error.log',
    out_file: '/home/<USER>/logs/api-out.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    max_memory_restart: '1G',
    autorestart: true,
    watch: false
  }]
};
EOF

# 创建日志目录
mkdir -p /home/<USER>/logs

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save
```

### 3.3 前端站点部署

#### 3.3.1 构建英语站点
```bash
cd /home/<USER>/pet-blog-system/frontend-en

# 配置环境变量
cp .env.example .env.production
vim .env.production
```

配置内容：
```env
PUBLIC_SITE_LANGUAGE=en
PUBLIC_SITE_URL=https://example.com
PUBLIC_API_URL=https://api.example.com/api/v1
PUBLIC_GA_ID=G-XXXXXXXXXX
PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXX
```

构建站点：
```bash
# 构建静态文件
pnpm build

# 创建部署目录
sudo mkdir -p /var/www/pet-blog-en
sudo chown -R deploy:deploy /var/www/pet-blog-en

# 复制构建文件
cp -r dist/* /var/www/pet-blog-en/
```

#### 3.3.2 构建德语站点
```bash
cd /home/<USER>/pet-blog-system/frontend-de

# 配置环境变量
cp .env.example .env.production
vim .env.production
```

配置内容：
```env
PUBLIC_SITE_LANGUAGE=de
PUBLIC_SITE_URL=https://example.de
PUBLIC_API_URL=https://api.example.com/api/v1
PUBLIC_GA_ID=G-XXXXXXXXXX
PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXX
```

构建并部署：
```bash
pnpm build
sudo mkdir -p /var/www/pet-blog-de
sudo chown -R deploy:deploy /var/www/pet-blog-de
cp -r dist/* /var/www/pet-blog-de/
```

#### 3.3.3 构建俄语站点
```bash
cd /home/<USER>/pet-blog-system/frontend-ru

# 配置环境变量
cp .env.example .env.production
vim .env.production
```

配置内容：
```env
PUBLIC_SITE_LANGUAGE=ru
PUBLIC_SITE_URL=https://example.ru
PUBLIC_API_URL=https://api.example.com/api/v1
PUBLIC_GA_ID=G-XXXXXXXXXX
PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXX
```

构建并部署：
```bash
pnpm build
sudo mkdir -p /var/www/pet-blog-ru
sudo chown -R deploy:deploy /var/www/pet-blog-ru
cp -r dist/* /var/www/pet-blog-ru/
```

### 3.4 管理后台部署

```bash
cd /home/<USER>/pet-blog-system/admin

# 配置环境变量
cp .env.example .env.production
vim .env.production
```

配置内容：
```env
VITE_API_URL=https://api.example.com/api/v1
VITE_APP_TITLE=Pet Blog Admin
```

构建并部署：
```bash
# 构建
pnpm build

# 创建部署目录
sudo mkdir -p /var/www/pet-blog-admin
sudo chown -R deploy:deploy /var/www/pet-blog-admin

# 复制文件
cp -r dist/* /var/www/pet-blog-admin/
```

## 4. Nginx配置

### 4.1 SSL证书申请

使用Let's Encrypt申请免费SSL证书：

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d example.com -d www.example.com
sudo certbot --nginx -d example.de -d www.example.de
sudo certbot --nginx -d example.ru -d www.example.ru
sudo certbot --nginx -d api.example.com
sudo certbot --nginx -d admin.example.com

# 设置自动续期
sudo systemctl enable certbot.timer
sudo systemctl start certbot.timer
```

### 4.2 Nginx站点配置

#### 4.2.1 API服务配置
```nginx
# /etc/nginx/sites-available/api.example.com
upstream pet_blog_api {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    keepalive 64;
}

server {
    listen 80;
    server_name api.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.example.com;

    ssl_certificate /etc/letsencrypt/live/api.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.example.com/privkey.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头部
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # API代理
    location / {
        proxy_pass http://pet_blog_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 请求体大小限制
        client_max_body_size 50M;
    }

    # 访问日志
    access_log /var/log/nginx/api.example.com.access.log;
    error_log /var/log/nginx/api.example.com.error.log;
}
```

#### 4.2.2 英语站点配置
```nginx
# /etc/nginx/sites-available/example.com
server {
    listen 80;
    server_name example.com www.example.com;
    return 301 https://example.com$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.example.com;
    
    ssl_certificate /etc/letsencrypt/live/example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
    
    return 301 https://example.com$request_uri;
}

server {
    listen 443 ssl http2;
    server_name example.com;
    
    root /var/www/pet-blog-en;
    index index.html;
    
    ssl_certificate /etc/letsencrypt/live/example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://pagead2.googlesyndication.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' https: data:; connect-src 'self' https://api.example.com https://www.google-analytics.com;" always;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;
    
    # 缓存配置
    location ~* \.(jpg|jpeg|png|gif|ico|webp|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(woff|woff2|ttf|otf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # 主要路由
    location / {
        try_files $uri $uri/ /index.html;
        
        # 预压缩文件支持
        gzip_static on;
    }
    
    # 移除尾部斜杠
    rewrite ^/(.*)/$ /$1 permanent;
    
    # robots.txt
    location = /robots.txt {
        add_header Content-Type text/plain;
    }
    
    # sitemap
    location ~ ^/sitemap.*\.xml$ {
        add_header Content-Type application/xml;
    }
    
    # 404错误页面
    error_page 404 /404.html;
    
    # 访问日志
    access_log /var/log/nginx/example.com.access.log;
    error_log /var/log/nginx/example.com.error.log;
}
```

#### 4.2.3 德语站点配置
```nginx
# /etc/nginx/sites-available/example.de
# 配置与英语站点类似，修改以下部分：
server_name example.de www.example.de;
root /var/www/pet-blog-de;
ssl_certificate /etc/letsencrypt/live/example.de/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/example.de/privkey.pem;
access_log /var/log/nginx/example.de.access.log;
error_log /var/log/nginx/example.de.error.log;
```

#### 4.2.4 俄语站点配置
```nginx
# /etc/nginx/sites-available/example.ru
# 配置与英语站点类似，修改以下部分：
server_name example.ru www.example.ru;
root /var/www/pet-blog-ru;
ssl_certificate /etc/letsencrypt/live/example.ru/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/example.ru/privkey.pem;
access_log /var/log/nginx/example.ru.access.log;
error_log /var/log/nginx/example.ru.error.log;
```

#### 4.2.5 管理后台配置
```nginx
# /etc/nginx/sites-available/admin.example.com
server {
    listen 80;
    server_name admin.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.example.com;
    
    root /var/www/pet-blog-admin;
    index index.html;
    
    ssl_certificate /etc/letsencrypt/live/admin.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.example.com/privkey.pem;
    
    # SSL和安全配置同上
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 限制访问（可选）
    # allow *******;  # 允许特定IP
    # deny all;       # 拒绝其他
    
    access_log /var/log/nginx/admin.example.com.access.log;
    error_log /var/log/nginx/admin.example.com.error.log;
}
```

### 4.3 启用站点配置

```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/api.example.com /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/example.com /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/example.de /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/example.ru /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/admin.example.com /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载Nginx
sudo systemctl reload nginx
```

## 5. 性能优化

### 5.1 Nginx性能优化

编辑 `/etc/nginx/nginx.conf`：

```nginx
user www-data;
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # 文件缓存
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    
    # Gzip设置
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=static:10m rate=30r/s;
    
    # 包含其他配置
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
```

### 5.2 系统优化

#### 5.2.1 内核参数优化
```bash
# 编辑 /etc/sysctl.conf
sudo vim /etc/sysctl.conf

# 添加以下配置
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 32768
net.core.somaxconn = 32768
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.ip_local_port_range = 1024 65535
net.ipv4.tcp_keepalive_time = 300
net.ipv4.tcp_keepalive_probes = 5
net.ipv4.tcp_keepalive_intvl = 15

# 应用配置
sudo sysctl -p
```

#### 5.2.2 文件描述符限制
```bash
# 编辑 /etc/security/limits.conf
sudo vim /etc/security/limits.conf

# 添加以下内容
* soft nofile 65535
* hard nofile 65535
* soft nproc 65535
* hard nproc 65535
```

### 5.3 CDN配置（可选）

如果使用Cloudflare CDN：

1. 在Cloudflare添加域名
2. 配置DNS记录
3. 启用以下功能：
   - Auto Minify（HTML/CSS/JS）
   - Brotli压缩
   - HTTP/3 (QUIC)
   - 0-RTT Connection Resumption
   - Rocket Loader™（谨慎使用）

页面规则配置：
- `example.com/*` - Cache Level: Cache Everything, Edge Cache TTL: 1 month
- `example.com/api/*` - Cache Level: Bypass
- `example.com/admin/*` - Cache Level: Bypass

## 6. 监控配置

### 6.1 日志监控

#### 6.1.1 配置日志轮转
```bash
# 创建logrotate配置
sudo vim /etc/logrotate.d/pet-blog

# 添加以下内容
/var/log/nginx/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data adm
    sharedscripts
    prerotate
        if [ -d /etc/logrotate.d/httpd-prerotate ]; then \
            run-parts /etc/logrotate.d/httpd-prerotate; \
        fi
    endscript
    postrotate
        invoke-rc.d nginx rotate >/dev/null 2>&1
    endscript
}

/home/<USER>/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 deploy deploy
}
```

### 6.2 性能监控

#### 6.2.1 安装监控工具
```bash
# 安装htop
sudo apt install -y htop

# 安装netdata（可选）
bash <(curl -Ss https://my-netdata.io/kickstart.sh)

# 安装PM2监控
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
```

#### 6.2.2 配置PM2监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 查看监控面板
pm2 monit

# 配置Web监控（可选）
pm2 install pm2-webshell
pm2 web
```

### 6.3 健康检查

#### 6.3.1 创建健康检查脚本
```bash
# 创建检查脚本
cat > /home/<USER>/health-check.sh << 'EOF'
#!/bin/bash

# API健康检查
API_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" https://api.example.com/health)
if [ $API_HEALTH -ne 200 ]; then
    echo "API health check failed: $API_HEALTH"
    # 发送告警（邮件/短信/Slack等）
fi

# 前端站点检查
SITES=("https://example.com" "https://example.de" "https://example.ru")
for site in "${SITES[@]}"; do
    SITE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $site)
    if [ $SITE_STATUS -ne 200 ]; then
        echo "$site health check failed: $SITE_STATUS"
        # 发送告警
    fi
done

# 磁盘空间检查
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage critical: $DISK_USAGE%"
    # 发送告警
fi

# 内存使用检查
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -gt 80 ]; then
    echo "Memory usage critical: $MEM_USAGE%"
    # 发送告警
fi
EOF

chmod +x /home/<USER>/health-check.sh

# 添加到crontab
crontab -e
# 添加：*/5 * * * * /home/<USER>/health-check.sh
```

## 7. 备份策略

### 7.1 数据库备份

#### 7.1.1 创建备份脚本
```bash
cat > /home/<USER>/backup-db.sh << 'EOF'
#!/bin/bash

# 配置
DB_HOST="************"
DB_USER="bengtai"
DB_PASS="weizhen258"
DB_NAME="bengtai"
BACKUP_DIR="/home/<USER>/backups/db"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# 删除30天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete

# 同步到远程存储（可选）
# rsync -avz $BACKUP_DIR/ remote-server:/path/to/backup/
EOF

chmod +x /home/<USER>/backup-db.sh

# 添加到crontab（每天凌晨2点执行）
crontab -e
# 添加：0 2 * * * /home/<USER>/backup-db.sh
```

### 7.2 文件备份

#### 7.2.1 媒体文件备份
```bash
cat > /home/<USER>/backup-files.sh << 'EOF'
#!/bin/bash

# 配置
SOURCE_DIR="/home/<USER>/pet-blog-system/uploads"
BACKUP_DIR="/home/<USER>/backups/files"
DATE=$(date +%Y%m%d)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 使用rsync增量备份
rsync -avz --delete $SOURCE_DIR/ $BACKUP_DIR/uploads_$DATE/

# 创建压缩包（每周执行）
if [ $(date +%w) -eq 0 ]; then
    tar -czf $BACKUP_DIR/uploads_weekly_$DATE.tar.gz $BACKUP_DIR/uploads_$DATE/
    # 删除4周前的周备份
    find $BACKUP_DIR -name "uploads_weekly_*.tar.gz" -mtime +28 -delete
fi

# 删除7天前的日备份
find $BACKUP_DIR -type d -name "uploads_*" -mtime +7 -exec rm -rf {} \;
EOF

chmod +x /home/<USER>/backup-files.sh

# 添加到crontab（每天凌晨3点执行）
crontab -e
# 添加：0 3 * * * /home/<USER>/backup-files.sh
```

### 7.3 配置文件备份

```bash
# 备份重要配置
tar -czf /home/<USER>/backups/configs_$(date +%Y%m%d).tar.gz \
    /etc/nginx/sites-available/ \
    /home/<USER>/pet-blog-system/backend/.env.production \
    /home/<USER>/pet-blog-system/*/ecosystem.config.js
```

## 8. 故障恢复

### 8.1 应用故障恢复

#### 8.1.1 API服务恢复
```bash
# 检查进程
pm2 status

# 重启服务
pm2 restart pet-blog-api

# 查看日志
pm2 logs pet-blog-api --lines 100

# 如果持续失败，检查：
# 1. 数据库连接
# 2. Redis连接
# 3. 端口占用
# 4. 环境变量配置
```

#### 8.1.2 Nginx故障恢复
```bash
# 检查配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 重启Nginx
sudo systemctl restart nginx

# 检查端口监听
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443
```

### 8.2 数据恢复

#### 8.2.1 数据库恢复
```bash
# 从备份恢复
gunzip < /home/<USER>/backups/db/backup_20240120_020000.sql.gz | mysql -h ************ -u bengtai -p bengtai

# 验证数据
mysql -h ************ -u bengtai -p -e "USE bengtai; SHOW TABLES;"
```

#### 8.2.2 文件恢复
```bash
# 恢复上传文件
rsync -avz /home/<USER>/backups/files/uploads_20240120/ /home/<USER>/pet-blog-system/uploads/

# 修复权限
chown -R deploy:deploy /home/<USER>/pet-blog-system/uploads/
```

## 9. 性能调优

### 9.1 数据库优化

连接到MySQL服务器执行以下优化：

```sql
-- 添加索引优化查询
ALTER TABLE articles ADD INDEX idx_status_published (status, published_at);
ALTER TABLE article_translations ADD INDEX idx_language_status (language, translation_status);
ALTER TABLE comments ADD INDEX idx_article_status (article_id, status);

-- 优化查询缓存
SET GLOBAL query_cache_size = 128M;
SET GLOBAL query_cache_type = 1;
```

### 9.2 Redis优化

编辑Redis配置：
```bash
sudo vim /etc/redis/redis.conf

# 优化配置
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 9.3 Node.js优化

PM2集群模式配置：
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'pet-blog-api',
    script: 'dist/main.js',
    instances: 'max', // 使用所有CPU核心
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      NODE_OPTIONS: '--max-old-space-size=2048'
    }
  }]
};
```

## 10. 安全加固

### 10.1 系统安全

#### 10.1.1 SSH安全
```bash
# 编辑SSH配置
sudo vim /etc/ssh/sshd_config

# 修改以下配置
Port 22345  # 修改默认端口
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
MaxAuthTries 3
ClientAliveInterval 600
ClientAliveCountMax 0

# 重启SSH服务
sudo systemctl restart sshd
```

#### 10.1.2 Fail2ban配置
```bash
# 安装fail2ban
sudo apt install -y fail2ban

# 创建本地配置
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
sudo vim /etc/fail2ban/jail.local

# 启用并配置规则
[sshd]
enabled = true
port = 22345
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/*error.log
maxretry = 10
bantime = 3600

# 启动服务
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 10.2 应用安全

#### 10.2.1 环境变量安全
```bash
# 设置严格的文件权限
chmod 600 /home/<USER>/pet-blog-system/backend/.env.production
chmod 600 /home/<USER>/pet-blog-system/frontend-*/.env.production
```

#### 10.2.2 定期更新
```bash
# 创建更新脚本
cat > /home/<USER>/update-system.sh << 'EOF'
#!/bin/bash

# 系统更新
sudo apt update && sudo apt upgrade -y

# Node.js包更新（谨慎执行）
cd /home/<USER>/pet-blog-system
pnpm update

# 重启服务
pm2 restart all
sudo systemctl reload nginx
EOF

chmod +x /home/<USER>/update-system.sh
```

## 11. 维护计划

### 11.1 日常维护任务

- **每日**：
  - 检查应用运行状态
  - 查看错误日志
  - 监控磁盘空间
  - 验证备份执行

- **每周**：
  - 分析访问日志
  - 清理临时文件
  - 检查安全更新
  - 性能指标评估

- **每月**：
  - 系统更新
  - SSL证书检查
  - 备份恢复测试
  - 安全审计

### 11.2 应急联系

创建应急联系文档：
```bash
cat > /home/<USER>/EMERGENCY.md << 'EOF'
# 应急联系信息

## 技术支持
- 系统管理员：[姓名] - [电话] - [邮箱]
- 开发负责人：[姓名] - [电话] - [邮箱]

## 服务商联系
- 服务器提供商：[名称] - [支持电话]
- 域名注册商：[名称] - [支持邮箱]
- CDN服务商：[名称] - [支持方式]

## 重要信息
- 服务器IP：[IP地址]
- 数据库备份位置：/home/<USER>/backups/db/
- 重要配置备份：/home/<USER>/backups/configs/

## 故障处理流程
1. 检查服务状态：pm2 status
2. 查看错误日志：pm2 logs
3. 检查Nginx：sudo systemctl status nginx
4. 验证数据库连接
5. 检查磁盘空间：df -h
6. 联系技术支持
EOF
```

## 12. 部署验证清单

### 12.1 功能验证

- [ ] API健康检查端点正常响应
- [ ] 所有语言站点可正常访问
- [ ] 文章列表正确显示
- [ ] 文章详情页正常加载
- [ ] 评论功能正常工作
- [ ] 搜索功能正常
- [ ] 管理后台可以登录
- [ ] 文章发布流程正常
- [ ] AI翻译功能正常
- [ ] 图片上传正常

### 12.2 性能验证

- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 500ms
- [ ] Google PageSpeed得分 > 90
- [ ] 所有静态资源启用压缩
- [ ] 所有静态资源设置缓存

### 12.3 安全验证

- [ ] HTTPS正确配置
- [ ] 安全头部正确设置
- [ ] API限流正常工作
- [ ] 管理后台访问受限
- [ ] 敏感文件权限正确

### 12.4 SEO验证

- [ ] robots.txt可访问
- [ ] sitemap.xml正确生成
- [ ] 元标签正确设置
- [ ] 结构化数据验证通过
- [ ] hreflang标签正确

## 总结

本部署指南详细说明了多语言宠物博客站群系统的完整部署流程，包括：

1. **环境准备**：服务器配置和软件安装
2. **项目部署**：后端API、前端站点和管理后台的部署
3. **Nginx配置**：多域名路由和SSL配置
4. **性能优化**：系统级和应用级优化
5. **监控备份**：完善的监控和备份策略
6. **安全加固**：多层次的安全防护
7. **维护计划**：日常维护和应急处理

通过遵循本指南，可以确保系统的稳定、安全和高性能运行。部署完成后，请务必执行验证清单，确保所有功能正常工作。