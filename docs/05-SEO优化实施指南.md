# 多语言宠物博客站群系统 - SEO优化实施指南

## 1. SEO优化概述

### 1.1 目标和原则
- **主要目标**：在Google搜索结果中获得高排名，吸引有机流量
- **核心原则**：用户体验优先、内容质量为王、技术优化为基础
- **关键指标**：Core Web Vitals、移动友好性、页面速度、内容相关性

### 1.2 2025年Google SEO最新要求
1. **E-E-A-T原则**：Experience（经验）、Expertise（专业性）、Authoritativeness（权威性）、Trustworthiness（可信度）
2. **Core Web Vitals**：LCP < 2.5s、FID < 100ms、CLS < 0.1
3. **移动优先索引**：确保移动端体验优于或等同于桌面端
4. **结构化数据**：丰富的Schema标记提升搜索结果展示
5. **用户体验信号**：跳出率、停留时间、点击率等

## 2. 技术SEO优化

### 2.1 URL结构优化

#### URL设计原则
```
✅ 正确的URL结构：
- 英语：https://example.com/cat-care/how-to-feed-your-cat
- 德语：https://example.de/katzenpflege/wie-fuettert-man-seine-katze  
- 俄语：https://example.ru/uhod-za-koshkami/kak-kormit-koshku

❌ 错误的URL结构：
- https://example.com/article?id=123
- https://example.com/en/cat-care/article-title
- https://example.com/категория/статья (非ASCII字符)
```

#### URL实现规范
```astro
---
// 生成SEO友好的URL slug
function generateSlug(title: string, language: string): string {
  let slug = title.toLowerCase();
  
  // 语言特定处理
  switch(language) {
    case 'de':
      // 德语特殊字符处理
      slug = slug
        .replace(/ä/g, 'ae')
        .replace(/ö/g, 'oe')
        .replace(/ü/g, 'ue')
        .replace(/ß/g, 'ss');
      break;
    case 'ru':
      // 俄语转写
      slug = transliterate(slug);
      break;
  }
  
  // 通用处理
  return slug
    .replace(/[^a-z0-9-]/g, '-')  // 替换特殊字符
    .replace(/-+/g, '-')           // 合并多个连字符
    .replace(/^-|-$/g, '')         // 移除首尾连字符
    .substring(0, 60);             // 限制长度
}
---
```

### 2.2 页面速度优化

#### 资源优化配置
```javascript
// astro.config.mjs - 性能优化配置
import { defineConfig } from 'astro/config';
import compress from 'astro-compress';
import critters from 'astro-critters';

export default defineConfig({
  integrations: [
    // HTML/CSS/JS压缩
    compress({
      css: true,
      html: {
        removeAttributeQuotes: false,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        sortAttributes: true,
        useShortDoctype: true,
        collapseWhitespace: true,
        minifyCSS: true,
        minifyJS: true
      },
      js: true,
      img: false, // 图片单独处理
      svg: true
    }),
    
    // 关键CSS内联
    critters({
      fonts: true,
      fontFace: true,
      keyframes: true,
      publicPath: '/',
      compress: true,
      logLevel: 'info'
    })
  ],
  
  // Vite优化配置
  vite: {
    build: {
      // 启用CSS代码分割
      cssCodeSplit: true,
      
      // 预加载策略
      modulePreload: {
        polyfill: true
      },
      
      // Rollup配置
      rollupOptions: {
        output: {
          // 资源文件名哈希
          assetFileNames: 'assets/[name].[hash][extname]',
          
          // 代码分割
          manualChunks: (id) => {
            // 将node_modules分离
            if (id.includes('node_modules')) {
              return 'vendor';
            }
            // 将工具函数分离
            if (id.includes('src/utils')) {
              return 'utils';
            }
          }
        }
      }
    },
    
    // 优化依赖预构建
    optimizeDeps: {
      include: ['alpinejs'],
      exclude: ['@astrojs/image']
    }
  }
});
```

#### 图片优化实施
```astro
---
// components/seo/OptimizedPicture.astro
export interface Props {
  src: string;
  alt: string;
  width: number;
  height: number;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  sizes?: string;
}

const { 
  src, 
  alt, 
  width, 
  height, 
  loading = 'lazy',
  priority = false,
  sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 800px'
} = Astro.props;

// 生成不同尺寸的图片URL
const imageSizes = [320, 640, 800, 1200, 1600];
const imageFormats = ['webp', 'jpg'];

// 如果是优先加载的图片，预连接到图片CDN
if (priority) {
  // 在head中添加预连接
}
---

<picture>
  {imageFormats.map(format => (
    <source
      type={`image/${format}`}
      srcset={imageSizes.map(size => 
        `${src}?w=${size}&fm=${format}&q=85 ${size}w`
      ).join(', ')}
      sizes={sizes}
    />
  ))}
  <img
    src={`${src}?w=${width}&fm=jpg&q=85`}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    decoding={priority ? 'sync' : 'async'}
    fetchpriority={priority ? 'high' : 'auto'}
    class="w-full h-auto"
  />
</picture>

<script>
  // 为关键图片添加预加载
  if (priority && 'connection' in navigator) {
    const connection = navigator.connection;
    if (connection.effectiveType === '4g') {
      // 在4G网络下预加载下一张图片
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.as = 'image';
      link.href = nextImageUrl;
      document.head.appendChild(link);
    }
  }
</script>
```

### 2.3 Core Web Vitals优化

#### LCP (Largest Contentful Paint) 优化
```astro
---
// layouts/Layout.astro - LCP优化
---
<head>
  <!-- 预连接到关键资源域名 -->
  <link rel="preconnect" href="https://api.example.com" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
  
  <!-- 预加载关键资源 -->
  <link rel="preload" as="font" type="font/woff2" 
    href="/fonts/inter-v12-latin-700.woff2" 
    crossorigin="anonymous" />
  
  <!-- 关键CSS内联 -->
  <style>
    /* 关键CSS - 首屏渲染所需 */
    :root {
      --color-primary: #2563eb;
      --max-width: 1280px;
    }
    
    body {
      margin: 0;
      font-family: Inter, system-ui, sans-serif;
    }
    
    .hero-image {
      width: 100%;
      height: auto;
      aspect-ratio: 16/9;
    }
  </style>
  
  <!-- 延迟加载非关键CSS -->
  <link rel="preload" href="/css/main.css" as="style" 
    onload="this.onload=null;this.rel='stylesheet'" />
  <noscript>
    <link rel="stylesheet" href="/css/main.css" />
  </noscript>
</head>

<body>
  <!-- LCP元素优先加载 -->
  <img 
    src="/hero.jpg?w=1200&fm=webp&q=85"
    alt="Hero Image"
    class="hero-image"
    fetchpriority="high"
    decoding="sync"
    width="1200"
    height="675"
  />
</body>
```

#### FID (First Input Delay) 优化
```javascript
// 代码分割和延迟加载交互功能
// components/interactive/CommentsSection.astro
<div id="comments-container" data-article-id={articleId}>
  <button id="load-comments" class="btn btn-secondary">
    Load Comments
  </button>
</div>

<script>
  // 延迟加载评论系统
  document.getElementById('load-comments')?.addEventListener('click', async () => {
    const { initComments } = await import('./comments.js');
    initComments(document.getElementById('comments-container'));
  }, { once: true });
  
  // 使用 Intersection Observer 自动加载
  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(async (entry) => {
        if (entry.isIntersecting) {
          const { initComments } = await import('./comments.js');
          initComments(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '100px'
    });
    
    const container = document.getElementById('comments-container');
    if (container) observer.observe(container);
  }
</script>
```

#### CLS (Cumulative Layout Shift) 优化
```css
/* 预留空间防止布局偏移 */
.article-image {
  aspect-ratio: 16 / 9;
  width: 100%;
  background-color: #f3f4f6;
}

.ad-container {
  min-height: 250px; /* 预留广告空间 */
  background-color: #f9fafb;
}

/* 字体加载优化 */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap; /* 防止FOIT */
  src: url('/fonts/inter-v12-latin-regular.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC;
}

/* 骨架屏防止内容跳动 */
.skeleton {
  animation: skeleton-loading 1s linear infinite alternate;
}

@keyframes skeleton-loading {
  0% {
    background-color: hsl(200, 20%, 80%);
  }
  100% {
    background-color: hsl(200, 20%, 95%);
  }
}
```

### 2.4 移动优化

#### 响应式设计实现
```astro
---
// components/layout/ResponsiveNavigation.astro
---
<nav class="navigation" x-data="{ mobileMenuOpen: false }">
  <div class="container mx-auto px-4">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <a href="/" class="text-xl font-bold">
        <img src="/logo.svg" alt="Pet Blog" width="150" height="40" />
      </a>
      
      <!-- Desktop Menu -->
      <ul class="hidden md:flex space-x-6">
        <li><a href="/cat-care" class="hover:text-primary-600">Cat Care</a></li>
        <li><a href="/dog-care" class="hover:text-primary-600">Dog Care</a></li>
        <li><a href="/about" class="hover:text-primary-600">About</a></li>
        <li><a href="/contact" class="hover:text-primary-600">Contact</a></li>
      </ul>
      
      <!-- Mobile Menu Button -->
      <button 
        @click="mobileMenuOpen = !mobileMenuOpen"
        class="md:hidden p-2"
        aria-label="Toggle menu"
        aria-expanded="false"
        :aria-expanded="mobileMenuOpen.toString()"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor">
          <path x-show="!mobileMenuOpen" d="M4 6h16M4 12h16M4 18h16" />
          <path x-show="mobileMenuOpen" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    
    <!-- Mobile Menu -->
    <div 
      x-show="mobileMenuOpen"
      x-transition:enter="transition ease-out duration-200"
      x-transition:enter-start="opacity-0 transform scale-95"
      x-transition:enter-end="opacity-100 transform scale-100"
      x-transition:leave="transition ease-in duration-150"
      x-transition:leave-start="opacity-100 transform scale-100"
      x-transition:leave-end="opacity-0 transform scale-95"
      class="md:hidden"
    >
      <ul class="py-4 space-y-2">
        <li><a href="/cat-care" class="block py-2 hover:text-primary-600">Cat Care</a></li>
        <li><a href="/dog-care" class="block py-2 hover:text-primary-600">Dog Care</a></li>
        <li><a href="/about" class="block py-2 hover:text-primary-600">About</a></li>
        <li><a href="/contact" class="block py-2 hover:text-primary-600">Contact</a></li>
      </ul>
    </div>
  </div>
</nav>

<style>
  /* 移动端触摸优化 */
  @media (hover: none) and (pointer: coarse) {
    .navigation a {
      padding: 0.75rem 1rem;
      margin: -0.75rem -1rem;
    }
    
    button {
      min-width: 44px;
      min-height: 44px;
    }
  }
</style>
```

## 3. 内容SEO优化

### 3.1 内容结构优化

#### 文章页面HTML结构
```astro
---
// pages/[category]/[...slug].astro
import Layout from '@/layouts/Layout.astro';
import ArticleSchema from '@/components/seo/ArticleSchema.astro';
import Breadcrumbs from '@/components/seo/Breadcrumbs.astro';

const { article } = Astro.props;

// 生成目录
const tableOfContents = generateTableOfContents(article.content);
---

<Layout 
  title={article.meta_title}
  description={article.meta_description}
  keywords={article.meta_keywords}
  type="article"
  image={article.featured_image}
>
  <ArticleSchema article={article} />
  
  <article class="article-container" itemscope itemtype="https://schema.org/Article">
    <!-- 面包屑导航 -->
    <Breadcrumbs items={[
      { name: 'Home', url: '/' },
      { name: article.category.name, url: `/${article.category.slug}` },
      { name: article.title, url: null }
    ]} />
    
    <!-- 文章标题 -->
    <header class="article-header">
      <h1 class="article-title" itemprop="headline">{article.title}</h1>
      
      <div class="article-meta">
        <span itemprop="author" itemscope itemtype="https://schema.org/Person">
          By <span itemprop="name">{article.author.username}</span>
        </span>
        <span class="separator">•</span>
        <time datetime={article.published_at} itemprop="datePublished">
          {formatDate(article.published_at)}
        </time>
        <span class="separator">•</span>
        <span>{article.view_count} views</span>
      </div>
    </header>
    
    <!-- 特色图片 -->
    <div class="article-image" itemprop="image" itemscope itemtype="https://schema.org/ImageObject">
      <img 
        src={article.featured_image}
        alt={article.title}
        width="1200"
        height="675"
        itemprop="url"
      />
      <meta itemprop="width" content="1200" />
      <meta itemprop="height" content="675" />
    </div>
    
    <!-- 文章摘要 -->
    <div class="article-excerpt" itemprop="description">
      {article.excerpt}
    </div>
    
    <!-- 目录 -->
    {tableOfContents.length > 0 && (
      <nav class="table-of-contents" aria-label="Table of contents">
        <h2>Table of Contents</h2>
        <ol>
          {tableOfContents.map(item => (
            <li>
              <a href={`#${item.id}`}>{item.text}</a>
              {item.children && (
                <ol>
                  {item.children.map(child => (
                    <li><a href={`#${child.id}`}>{child.text}</a></li>
                  ))}
                </ol>
              )}
            </li>
          ))}
        </ol>
      </nav>
    )}
    
    <!-- 文章内容 -->
    <div class="article-content" itemprop="articleBody">
      <Fragment set:html={optimizeContent(article.content)} />
    </div>
    
    <!-- 相关标签 -->
    <div class="article-tags">
      <h3>Related Topics:</h3>
      <ul class="tag-list">
        {article.tags.map(tag => (
          <li>
            <a href={`/tags/${tag}`} rel="tag">#{tag}</a>
          </li>
        ))}
      </ul>
    </div>
    
    <!-- 作者信息 -->
    <div class="author-bio" itemprop="author" itemscope itemtype="https://schema.org/Person">
      <img 
        src={article.author.avatar}
        alt={article.author.username}
        itemprop="image"
        width="80"
        height="80"
      />
      <div>
        <h3 itemprop="name">{article.author.username}</h3>
        <p itemprop="description">{article.author.bio}</p>
      </div>
    </div>
  </article>
  
  <!-- 相关文章 -->
  <section class="related-articles">
    <h2>Related Articles</h2>
    <!-- 相关文章列表 -->
  </section>
</Layout>

<script>
  // 为标题添加ID以支持锚点链接
  function optimizeContent(content: string): string {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    
    // 为所有标题添加ID
    const headings = doc.querySelectorAll('h2, h3, h4');
    headings.forEach((heading, index) => {
      const id = heading.textContent
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      heading.id = id;
    });
    
    // 优化图片
    const images = doc.querySelectorAll('img');
    images.forEach(img => {
      img.loading = 'lazy';
      img.decoding = 'async';
      
      // 添加尺寸属性防止CLS
      if (!img.width) img.width = '800';
      if (!img.height) img.height = '450';
    });
    
    // 外部链接添加属性
    const links = doc.querySelectorAll('a[href^="http"]');
    links.forEach(link => {
      link.setAttribute('rel', 'noopener noreferrer');
      link.setAttribute('target', '_blank');
    });
    
    return doc.body.innerHTML;
  }
</script>
```

### 3.2 结构化数据实现

#### Article Schema实现
```astro
---
// components/seo/ArticleSchema.astro
export interface Props {
  article: Article;
}

const { article } = Astro.props;
const siteConfig = await import('@/config/site');

const structuredData = {
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": article.title,
  "description": article.excerpt,
  "image": {
    "@type": "ImageObject",
    "url": article.featured_image,
    "width": 1200,
    "height": 675
  },
  "datePublished": article.published_at,
  "dateModified": article.updated_at,
  "author": {
    "@type": "Person",
    "name": article.author.username,
    "url": `${siteConfig.siteUrl}/authors/${article.author.username}`
  },
  "publisher": {
    "@type": "Organization",
    "name": siteConfig.siteName,
    "logo": {
      "@type": "ImageObject",
      "url": `${siteConfig.siteUrl}/logo.png`,
      "width": 600,
      "height": 60
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": `${siteConfig.siteUrl}/${article.category.slug}/${article.slug}`
  },
  "keywords": article.meta_keywords,
  "articleSection": article.category.name,
  "wordCount": countWords(article.content),
  "inLanguage": siteConfig.language,
  "potentialAction": {
    "@type": "CommentAction",
    "name": "Comment",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": `${siteConfig.siteUrl}/${article.category.slug}/${article.slug}#comments`
    }
  }
};

// 添加FAQ结构化数据（如果有）
if (article.faqs && article.faqs.length > 0) {
  structuredData["@graph"] = [
    structuredData,
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": article.faqs.map(faq => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer
        }
      }))
    }
  ];
}
---

<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
```

#### BreadcrumbList Schema
```astro
---
// components/seo/BreadcrumbSchema.astro
export interface Props {
  items: Array<{
    name: string;
    url: string | null;
  }>;
}

const { items } = Astro.props;
const siteConfig = await import('@/config/site');

const breadcrumbData = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": items.map((item, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": item.name,
    "item": item.url ? `${siteConfig.siteUrl}${item.url}` : undefined
  }))
};
---

<script type="application/ld+json" set:html={JSON.stringify(breadcrumbData)} />
```

### 3.3 元数据优化

#### 动态元标签生成
```astro
---
// components/seo/DynamicMetaTags.astro
export interface Props {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  type?: 'website' | 'article';
  article?: {
    author: string;
    publishedTime: string;
    modifiedTime: string;
    section: string;
    tags: string[];
  };
}

const {
  title,
  description,
  keywords,
  image,
  type = 'website',
  article
} = Astro.props;

const siteConfig = await import('@/config/site');

// 优化标题长度
const optimizedTitle = title.length > 60 
  ? title.substring(0, 57) + '...' 
  : title;

// 优化描述长度
const optimizedDescription = description.length > 160
  ? description.substring(0, 157) + '...'
  : description;

// 生成规范化URL
const canonicalURL = new URL(Astro.url.pathname, siteConfig.siteUrl).toString();

// 语言变体URLs
const languageVariants = {
  'en': `https://example.com${Astro.url.pathname}`,
  'de': `https://example.de${Astro.url.pathname}`,
  'ru': `https://example.ru${Astro.url.pathname}`
};
---

<!-- 基础元标签 -->
<title>{optimizedTitle} | {siteConfig.siteName}</title>
<meta name="description" content={optimizedDescription} />
{keywords && <meta name="keywords" content={keywords} />}
<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
<link rel="canonical" href={canonicalURL} />

<!-- Open Graph -->
<meta property="og:title" content={optimizedTitle} />
<meta property="og:description" content={optimizedDescription} />
<meta property="og:type" content={type} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:site_name" content={siteConfig.siteName} />
<meta property="og:locale" content={siteConfig.locale} />
{image && <meta property="og:image" content={image} />}
{image && <meta property="og:image:width" content="1200" />}
{image && <meta property="og:image:height" content="675" />}

<!-- Article特定标签 -->
{type === 'article' && article && (
  <>
    <meta property="article:author" content={article.author} />
    <meta property="article:published_time" content={article.publishedTime} />
    <meta property="article:modified_time" content={article.modifiedTime} />
    <meta property="article:section" content={article.section} />
    {article.tags.map(tag => (
      <meta property="article:tag" content={tag} />
    ))}
  </>
)}

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={optimizedTitle} />
<meta name="twitter:description" content={optimizedDescription} />
{image && <meta name="twitter:image" content={image} />}
{siteConfig.twitterHandle && <meta name="twitter:site" content={siteConfig.twitterHandle} />}

<!-- 语言标签 -->
{Object.entries(languageVariants).map(([lang, url]) => (
  <link rel="alternate" hreflang={lang} href={url} />
))}
<link rel="alternate" hreflang="x-default" href={languageVariants['en']} />

<!-- 其他SEO标签 -->
<meta name="author" content={article?.author || siteConfig.siteName} />
<meta name="generator" content="Astro" />
<meta name="format-detection" content="telephone=no" />
```

## 4. 内容优化策略

### 4.1 关键词研究和优化

#### 关键词密度优化
```typescript
// utils/seo/keywordOptimizer.ts
interface KeywordAnalysis {
  keyword: string;
  count: number;
  density: number;
  positions: number[];
}

export function analyzeKeywords(content: string, targetKeywords: string[]): KeywordAnalysis[] {
  const words = content.toLowerCase().split(/\s+/);
  const totalWords = words.length;
  
  return targetKeywords.map(keyword => {
    const keywordLower = keyword.toLowerCase();
    const regex = new RegExp(`\\b${keywordLower}\\b`, 'gi');
    const matches = content.match(regex) || [];
    const positions: number[] = [];
    
    let index = content.toLowerCase().indexOf(keywordLower);
    while (index !== -1) {
      positions.push(index);
      index = content.toLowerCase().indexOf(keywordLower, index + 1);
    }
    
    return {
      keyword,
      count: matches.length,
      density: (matches.length / totalWords) * 100,
      positions
    };
  });
}

export function optimizeKeywordPlacement(
  content: string, 
  targetKeyword: string, 
  targetDensity: number = 2
): string {
  const analysis = analyzeKeywords(content, [targetKeyword])[0];
  
  // 检查关键词是否出现在重要位置
  const hasInTitle = /<h1[^>]*>.*?targetKeyword.*?<\/h1>/i.test(content);
  const hasInFirstParagraph = content.substring(0, 200).toLowerCase().includes(targetKeyword.toLowerCase());
  const hasInLastParagraph = content.substring(content.length - 200).toLowerCase().includes(targetKeyword.toLowerCase());
  
  // 建议
  const suggestions = [];
  if (!hasInTitle) suggestions.push('Add keyword to main title');
  if (!hasInFirstParagraph) suggestions.push('Add keyword to first paragraph');
  if (!hasInLastParagraph) suggestions.push('Add keyword to conclusion');
  if (analysis.density < targetDensity - 0.5) suggestions.push('Increase keyword usage');
  if (analysis.density > targetDensity + 0.5) suggestions.push('Reduce keyword usage');
  
  return suggestions.join(', ');
}
```

### 4.2 内容质量优化

#### 可读性评分
```typescript
// utils/seo/readabilityScorer.ts
export function calculateReadabilityScore(content: string): {
  score: number;
  grade: string;
  suggestions: string[];
} {
  // 移除HTML标签
  const text = content.replace(/<[^>]*>/g, '');
  
  // 计算指标
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = text.split(/\s+/).filter(w => w.length > 0);
  const syllables = countSyllables(text);
  
  const avgWordsPerSentence = words.length / sentences.length;
  const avgSyllablesPerWord = syllables / words.length;
  
  // Flesch Reading Ease Score
  const fleschScore = 206.835 - 1.015 * avgWordsPerSentence - 84.6 * avgSyllablesPerWord;
  
  // 评级
  let grade = '';
  let suggestions = [];
  
  if (fleschScore >= 90) {
    grade = 'Very Easy';
  } else if (fleschScore >= 80) {
    grade = 'Easy';
  } else if (fleschScore >= 70) {
    grade = 'Fairly Easy';
  } else if (fleschScore >= 60) {
    grade = 'Standard';
  } else if (fleschScore >= 50) {
    grade = 'Fairly Difficult';
    suggestions.push('Consider using shorter sentences');
    suggestions.push('Use simpler words when possible');
  } else if (fleschScore >= 30) {
    grade = 'Difficult';
    suggestions.push('Break long sentences into shorter ones');
    suggestions.push('Replace complex words with simpler alternatives');
  } else {
    grade = 'Very Difficult';
    suggestions.push('Significantly simplify your writing');
    suggestions.push('Target 8th-grade reading level for better engagement');
  }
  
  // 额外建议
  if (avgWordsPerSentence > 20) {
    suggestions.push(`Average sentence length is ${avgWordsPerSentence.toFixed(1)} words. Aim for 15-20.`);
  }
  
  // 段落长度检查
  const paragraphs = text.split(/\n\n+/);
  const longParagraphs = paragraphs.filter(p => p.split(/\s+/).length > 150);
  if (longParagraphs.length > 0) {
    suggestions.push(`${longParagraphs.length} paragraphs are too long. Break them up.`);
  }
  
  return {
    score: Math.max(0, Math.min(100, fleschScore)),
    grade,
    suggestions
  };
}

function countSyllables(text: string): number {
  // 简化的音节计数算法
  const words = text.toLowerCase().split(/\s+/);
  let totalSyllables = 0;
  
  words.forEach(word => {
    word = word.replace(/[^a-z]/g, '');
    if (word.length <= 3) {
      totalSyllables += 1;
    } else {
      // 计算元音组
      const vowelGroups = word.match(/[aeiouy]+/g) || [];
      totalSyllables += vowelGroups.length;
      
      // 调整静音e
      if (word.endsWith('e')) totalSyllables--;
      
      // 确保至少1个音节
      if (totalSyllables < 1) totalSyllables = 1;
    }
  });
  
  return totalSyllables;
}
```

### 4.3 内链优化

#### 自动内链生成
```typescript
// utils/seo/internalLinking.ts
interface InternalLink {
  text: string;
  url: string;
  title: string;
}

export async function generateInternalLinks(
  content: string,
  currentArticleId: number,
  language: string
): Promise<string> {
  // 获取相关文章
  const relatedArticles = await api.getRelatedArticles(currentArticleId, language);
  
  // 创建关键词到URL的映射
  const linkMap = new Map<string, InternalLink>();
  
  relatedArticles.forEach(article => {
    // 主关键词
    const mainKeyword = article.title.toLowerCase();
    linkMap.set(mainKeyword, {
      text: article.title,
      url: `/${article.category.slug}/${article.slug}`,
      title: article.meta_title
    });
    
    // 相关关键词
    if (article.keywords) {
      article.keywords.split(',').forEach(keyword => {
        const trimmedKeyword = keyword.trim().toLowerCase();
        if (!linkMap.has(trimmedKeyword)) {
          linkMap.set(trimmedKeyword, {
            text: keyword.trim(),
            url: `/${article.category.slug}/${article.slug}`,
            title: article.meta_title
          });
        }
      });
    }
  });
  
  // 在内容中添加链接
  let modifiedContent = content;
  const addedLinks = new Set<string>();
  const maxLinksPerArticle = 5;
  let linkCount = 0;
  
  // 按关键词长度排序（长的优先）
  const sortedKeywords = Array.from(linkMap.keys()).sort((a, b) => b.length - a.length);
  
  sortedKeywords.forEach(keyword => {
    if (linkCount >= maxLinksPerArticle) return;
    
    const link = linkMap.get(keyword)!;
    if (addedLinks.has(link.url)) return;
    
    // 创建正则表达式，确保只匹配完整单词
    const regex = new RegExp(`\\b(${keyword})\\b(?![^<]*>)`, 'gi');
    
    // 只替换第一次出现
    modifiedContent = modifiedContent.replace(regex, (match, p1, offset) => {
      // 检查是否已经在链接内
      const beforeText = modifiedContent.substring(0, offset);
      const inLink = /<a[^>]*>[^<]*$/.test(beforeText);
      
      if (!inLink && !addedLinks.has(link.url)) {
        addedLinks.add(link.url);
        linkCount++;
        return `<a href="${link.url}" title="${link.title}" class="internal-link">${match}</a>`;
      }
      
      return match;
    });
  });
  
  return modifiedContent;
}
```

## 5. 技术实施细节

### 5.1 robots.txt配置
```text
# robots.txt for English site (example.com)
User-agent: *
Allow: /
Disallow: /api/
Disallow: /admin/
Disallow: /search?
Disallow: /*?page=
Disallow: /*?sort=
Allow: /search

# 爬虫延迟
Crawl-delay: 1

# 站点地图
Sitemap: https://example.com/sitemap-index.xml

# 针对特定爬虫的规则
User-agent: Googlebot
Allow: /
Crawl-delay: 0

User-agent: Googlebot-Image
Allow: /
Disallow: /images/admin/

# 屏蔽恶意爬虫
User-agent: AhrefsBot
Disallow: /

User-agent: SemrushBot
Disallow: /
```

### 5.2 XML Sitemap生成
```astro
---
// pages/sitemap-index.xml.ts
import type { APIRoute } from 'astro';

export const get: APIRoute = async ({ site }) => {
  const sitemaps = [
    'sitemap-articles.xml',
    'sitemap-categories.xml',
    'sitemap-pages.xml',
    'sitemap-images.xml'
  ];
  
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps.map(sitemap => `  <sitemap>
    <loc>${site}${sitemap}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`).join('\n')}
</sitemapindex>`;

  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600'
    }
  });
};
```

#### 文章Sitemap生成
```astro
---
// pages/sitemap-articles.xml.ts
import type { APIRoute } from 'astro';
import { api } from '@/utils/api';

export const get: APIRoute = async ({ site }) => {
  const language = process.env.SITE_LANGUAGE || 'en';
  const { data: articles } = await api.getArticles({ 
    per_page: 10000,
    status: 'published' 
  });
  
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
${articles.map(article => {
  const url = `${site}${article.category.slug}/${article.slug}`;
  const lastmod = new Date(article.updated_at).toISOString();
  const priority = calculatePriority(article);
  const changefreq = calculateChangeFreq(article);
  
  return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
    ${article.featured_image ? `<image:image>
      <image:loc>${article.featured_image}</image:loc>
      <image:title>${escapeXml(article.title)}</image:title>
      <image:caption>${escapeXml(article.excerpt)}</image:caption>
    </image:image>` : ''}
  </url>`;
}).join('\n')}
</urlset>`;

  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600'
    }
  });
};

function calculatePriority(article: Article): number {
  const age = Date.now() - new Date(article.published_at).getTime();
  const daysSincePublished = age / (1000 * 60 * 60 * 24);
  
  if (daysSincePublished < 7) return 1.0;
  if (daysSincePublished < 30) return 0.9;
  if (daysSincePublished < 90) return 0.8;
  if (daysSincePublished < 365) return 0.7;
  return 0.6;
}

function calculateChangeFreq(article: Article): string {
  const daysSinceUpdate = (Date.now() - new Date(article.updated_at).getTime()) / (1000 * 60 * 60 * 24);
  
  if (daysSinceUpdate < 1) return 'hourly';
  if (daysSinceUpdate < 7) return 'daily';
  if (daysSinceUpdate < 30) return 'weekly';
  if (daysSinceUpdate < 365) return 'monthly';
  return 'yearly';
}

function escapeXml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}
```

### 5.3 重定向和规范化

#### 重定向配置
```javascript
// astro.config.mjs - 重定向规则
export default defineConfig({
  redirects: {
    // WWW到非WWW重定向
    'https://www.example.com/*': 'https://example.com/:splat',
    
    // 旧URL到新URL重定向
    '/blog/*': '/articles/:splat',
    '/category/*': '/categories/:splat',
    
    // 移除尾部斜杠
    '*/': '/:splat',
    
    // 特定文章重定向
    '/old-article-url': '/cat-care/new-article-url'
  }
});
```

#### Nginx重定向配置
```nginx
# nginx.conf - SEO重定向规则
server {
    server_name www.example.com;
    return 301 https://example.com$request_uri;
}

server {
    server_name example.com;
    
    # HTTPS重定向
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }
    
    # 移除尾部斜杠
    rewrite ^/(.*)/$ /$1 permanent;
    
    # 移除index.html
    rewrite ^(.*)/index\.html$ $1 permanent;
    
    # 旧URL重定向映射
    map $request_uri $redirect_uri {
        /old-category/old-article /cat-care/new-article;
        /obsolete-page /;
    }
    
    if ($redirect_uri) {
        return 301 $redirect_uri;
    }
}
```

## 6. 监控和分析

### 6.1 Google Search Console集成

#### 验证代码
```astro
---
// layouts/Layout.astro - Search Console验证
const googleVerification = 'your-verification-code';
---
<head>
  <meta name="google-site-verification" content={googleVerification} />
</head>
```

#### 结构化数据测试
```typescript
// utils/seo/structuredDataValidator.ts
export async function validateStructuredData(url: string): Promise<{
  valid: boolean;
  errors: string[];
  warnings: string[];
}> {
  try {
    // 使用Google的结构化数据测试API
    const response = await fetch(
      `https://search.google.com/structured-data/testing-tool/validate`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url })
      }
    );
    
    const result = await response.json();
    
    return {
      valid: result.errors.length === 0,
      errors: result.errors,
      warnings: result.warnings
    };
  } catch (error) {
    console.error('Structured data validation failed:', error);
    return {
      valid: false,
      errors: ['Validation failed'],
      warnings: []
    };
  }
}
```

### 6.2 性能监控

#### Web Vitals监控
```astro
---
// components/analytics/WebVitals.astro
---
<script>
  import { getCLS, getFID, getLCP, getFCP, getTTFB } from 'web-vitals';
  
  function sendToAnalytics(metric) {
    // 发送到Google Analytics
    if (window.gtag) {
      gtag('event', metric.name, {
        value: Math.round(metric.value),
        metric_id: metric.id,
        metric_value: metric.value,
        metric_delta: metric.delta,
      });
    }
    
    // 发送到自定义分析端点
    fetch('/api/analytics/vitals', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        metric: metric.name,
        value: metric.value,
        id: metric.id,
        url: window.location.href,
        timestamp: new Date().toISOString()
      })
    });
  }
  
  // 监控 Core Web Vitals
  getCLS(sendToAnalytics);
  getFID(sendToAnalytics);
  getLCP(sendToAnalytics);
  
  // 额外的性能指标
  getFCP(sendToAnalytics);
  getTTFB(sendToAnalytics);
  
  // 自定义性能标记
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'measure') {
          sendToAnalytics({
            name: entry.name,
            value: entry.duration,
            id: crypto.randomUUID()
          });
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure'] });
  }
</script>
```

### 6.3 SEO报告生成

#### 自动SEO审计
```typescript
// utils/seo/seoAuditor.ts
interface SEOAuditResult {
  score: number;
  issues: SEOIssue[];
  suggestions: string[];
}

interface SEOIssue {
  type: 'error' | 'warning' | 'info';
  category: string;
  message: string;
  element?: string;
}

export async function auditPage(url: string): Promise<SEOAuditResult> {
  const issues: SEOIssue[] = [];
  const suggestions: string[] = [];
  let score = 100;
  
  // 获取页面内容
  const response = await fetch(url);
  const html = await response.text();
  const doc = new DOMParser().parseFromString(html, 'text/html');
  
  // 检查标题
  const title = doc.querySelector('title')?.textContent || '';
  if (!title) {
    issues.push({
      type: 'error',
      category: 'Meta',
      message: 'Missing page title'
    });
    score -= 10;
  } else if (title.length > 60) {
    issues.push({
      type: 'warning',
      category: 'Meta',
      message: `Title too long (${title.length} characters)`,
      element: title
    });
    score -= 5;
  }
  
  // 检查描述
  const description = doc.querySelector('meta[name="description"]')?.getAttribute('content') || '';
  if (!description) {
    issues.push({
      type: 'error',
      category: 'Meta',
      message: 'Missing meta description'
    });
    score -= 10;
  } else if (description.length > 160) {
    issues.push({
      type: 'warning',
      category: 'Meta',
      message: `Description too long (${description.length} characters)`,
      element: description
    });
    score -= 5;
  }
  
  // 检查H1标签
  const h1Tags = doc.querySelectorAll('h1');
  if (h1Tags.length === 0) {
    issues.push({
      type: 'error',
      category: 'Content',
      message: 'Missing H1 tag'
    });
    score -= 10;
  } else if (h1Tags.length > 1) {
    issues.push({
      type: 'warning',
      category: 'Content',
      message: `Multiple H1 tags found (${h1Tags.length})`
    });
    score -= 5;
  }
  
  // 检查图片ALT属性
  const images = doc.querySelectorAll('img');
  const imagesWithoutAlt = Array.from(images).filter(img => !img.getAttribute('alt'));
  if (imagesWithoutAlt.length > 0) {
    issues.push({
      type: 'warning',
      category: 'Images',
      message: `${imagesWithoutAlt.length} images missing alt text`
    });
    score -= imagesWithoutAlt.length * 2;
  }
  
  // 检查结构化数据
  const structuredData = doc.querySelectorAll('script[type="application/ld+json"]');
  if (structuredData.length === 0) {
    issues.push({
      type: 'warning',
      category: 'Structured Data',
      message: 'No structured data found'
    });
    score -= 5;
  }
  
  // 生成建议
  if (score < 90) suggestions.push('Address critical SEO issues');
  if (score < 80) suggestions.push('Improve meta tags and content structure');
  if (score < 70) suggestions.push('Add structured data and optimize images');
  
  return {
    score: Math.max(0, score),
    issues,
    suggestions
  };
}
```

## 7. 多语言SEO策略

### 7.1 Hreflang实现

#### 自动Hreflang生成
```astro
---
// components/seo/HreflangTags.astro
export interface Props {
  currentLanguage: string;
  alternateUrls: {
    [language: string]: string;
  };
}

const { currentLanguage, alternateUrls } = Astro.props;

// 确保包含当前语言
if (!alternateUrls[currentLanguage]) {
  alternateUrls[currentLanguage] = Astro.url.href;
}

// 添加x-default
const defaultLanguage = 'en';
---

{Object.entries(alternateUrls).map(([lang, url]) => (
  <link rel="alternate" hreflang={lang} href={url} />
))}
<link rel="alternate" hreflang="x-default" href={alternateUrls[defaultLanguage]} />
```

### 7.2 本地化内容优化

#### 语言特定的SEO优化
```typescript
// utils/seo/localizedSEO.ts
interface LocalizedSEOConfig {
  language: string;
  stopWords: string[];
  keywordStemmer: (word: string) => string;
  titleTemplate: string;
  descriptionTemplate: string;
}

const seoConfigs: { [lang: string]: LocalizedSEOConfig } = {
  en: {
    language: 'en',
    stopWords: ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at'],
    keywordStemmer: (word) => {
      // 英语词干提取
      return word.replace(/ing$|ed$|s$/, '');
    },
    titleTemplate: '{title} | {category} - {site}',
    descriptionTemplate: 'Learn about {topic}. {summary} Read more on {site}.'
  },
  de: {
    language: 'de',
    stopWords: ['der', 'die', 'das', 'und', 'oder', 'aber', 'in', 'auf'],
    keywordStemmer: (word) => {
      // 德语词干提取
      return word.replace(/en$|er$|es$/, '');
    },
    titleTemplate: '{title} | {category} - {site}',
    descriptionTemplate: 'Erfahren Sie mehr über {topic}. {summary} Mehr auf {site}.'
  },
  ru: {
    language: 'ru',
    stopWords: ['и', 'в', 'на', 'с', 'по', 'для', 'или', 'но'],
    keywordStemmer: (word) => {
      // 俄语词干提取（简化版）
      return word.replace(/[аяыи]$/, '');
    },
    titleTemplate: '{title} | {category} - {site}',
    descriptionTemplate: 'Узнайте о {topic}. {summary} Читайте на {site}.'
  }
};

export function optimizeForLanguage(
  content: string,
  language: string,
  keywords: string[]
): {
  optimizedContent: string;
  keywordDensity: { [keyword: string]: number };
  suggestions: string[];
} {
  const config = seoConfigs[language];
  if (!config) throw new Error(`Unsupported language: ${language}`);
  
  // 移除停用词进行分析
  const words = content.toLowerCase()
    .split(/\s+/)
    .filter(word => !config.stopWords.includes(word));
  
  // 计算关键词密度
  const keywordDensity: { [keyword: string]: number } = {};
  keywords.forEach(keyword => {
    const stemmedKeyword = config.keywordStemmer(keyword.toLowerCase());
    const count = words.filter(word => 
      config.keywordStemmer(word) === stemmedKeyword
    ).length;
    keywordDensity[keyword] = (count / words.length) * 100;
  });
  
  // 生成优化建议
  const suggestions: string[] = [];
  Object.entries(keywordDensity).forEach(([keyword, density]) => {
    if (density < 1) {
      suggestions.push(`Increase usage of "${keyword}"`);
    } else if (density > 3) {
      suggestions.push(`Reduce usage of "${keyword}" to avoid keyword stuffing`);
    }
  });
  
  return {
    optimizedContent: content,
    keywordDensity,
    suggestions
  };
}
```

## 8. 高级SEO技术

### 8.1 语音搜索优化

#### FAQ Schema实现
```astro
---
// components/seo/FAQSchema.astro
export interface Props {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

const { faqs } = Astro.props;

const faqSchema = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
};
---

<script type="application/ld+json" set:html={JSON.stringify(faqSchema)} />

<!-- FAQ内容展示 -->
<section class="faq-section" itemscope itemtype="https://schema.org/FAQPage">
  <h2>Frequently Asked Questions</h2>
  {faqs.map((faq, index) => (
    <div 
      class="faq-item" 
      itemscope 
      itemprop="mainEntity" 
      itemtype="https://schema.org/Question"
    >
      <h3 itemprop="name">{faq.question}</h3>
      <div 
        itemscope 
        itemprop="acceptedAnswer" 
        itemtype="https://schema.org/Answer"
      >
        <p itemprop="text">{faq.answer}</p>
      </div>
    </div>
  ))}
</section>
```

### 8.2 特色片段优化

#### 内容格式化优化
```typescript
// utils/seo/featuredSnippetOptimizer.ts
export function optimizeForFeaturedSnippets(content: string): string {
  // 添加定义框
  content = content.replace(
    /what is ([^.?]+)[.?]/gi,
    (match, term) => {
      return `<div class="definition-box">
        <strong>What is ${term}?</strong>
        <p>${match}</p>
      </div>`;
    }
  );
  
  // 格式化步骤列表
  content = content.replace(
    /(?:steps?|how to)([^:]+):([\s\S]+?)(?=\n\n|$)/gi,
    (match, title, steps) => {
      const stepList = steps.trim().split(/\n/).map((step, index) => {
        const cleanStep = step.replace(/^\d+\.\s*/, '');
        return `<li><strong>Step ${index + 1}:</strong> ${cleanStep}</li>`;
      }).join('\n');
      
      return `<div class="how-to-box">
        <h3>How to${title}</h3>
        <ol>${stepList}</ol>
      </div>`;
    }
  );
  
  // 创建比较表格
  content = content.replace(
    /comparison of ([^:]+):([\s\S]+?)(?=\n\n|$)/gi,
    (match, items, comparison) => {
      // 解析比较内容并创建表格
      return `<div class="comparison-table">
        <h3>Comparison of ${items}</h3>
        <table>
          <!-- 表格内容 -->
        </table>
      </div>`;
    }
  );
  
  return content;
}
```

### 8.3 实体SEO优化

#### Knowledge Graph优化
```astro
---
// components/seo/OrganizationSchema.astro
const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Pet Blog",
  "url": "https://example.com",
  "logo": "https://example.com/logo.png",
  "description": "Your trusted source for pet care information",
  "foundingDate": "2024",
  "founders": [{
    "@type": "Person",
    "name": "Founder Name"
  }],
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "US"
  },
  "contactPoint": [{
    "@type": "ContactPoint",
    "telephone": "+1-xxx-xxx-xxxx",
    "contactType": "customer service",
    "areaServed": ["US", "DE", "RU"],
    "availableLanguage": ["en", "de", "ru"]
  }],
  "sameAs": [
    "https://facebook.com/petblog",
    "https://twitter.com/petblog",
    "https://instagram.com/petblog"
  ],
  "knowsAbout": [
    "Cat Care",
    "Dog Care",
    "Pet Health",
    "Pet Training"
  ]
};
---

<script type="application/ld+json" set:html={JSON.stringify(organizationSchema)} />
```

## 9. SEO测试和验证

### 9.1 自动化SEO测试

#### Playwright SEO测试
```typescript
// tests/seo/seo.spec.ts
import { test, expect } from '@playwright/test';

test.describe('SEO Tests', () => {
  test('Homepage SEO elements', async ({ page }) => {
    await page.goto('/');
    
    // 检查标题
    const title = await page.title();
    expect(title).toContain('Pet Blog');
    expect(title.length).toBeLessThanOrEqual(60);
    
    // 检查元描述
    const description = await page.$eval(
      'meta[name="description"]',
      el => el.getAttribute('content')
    );
    expect(description).toBeTruthy();
    expect(description.length).toBeLessThanOrEqual(160);
    
    // 检查H1
    const h1Count = await page.$$eval('h1', elements => elements.length);
    expect(h1Count).toBe(1);
    
    // 检查结构化数据
    const structuredData = await page.$$eval(
      'script[type="application/ld+json"]',
      elements => elements.map(el => JSON.parse(el.textContent))
    );
    expect(structuredData.length).toBeGreaterThan(0);
    
    // 检查canonical标签
    const canonical = await page.$eval(
      'link[rel="canonical"]',
      el => el.getAttribute('href')
    );
    expect(canonical).toBeTruthy();
  });
  
  test('Article page SEO', async ({ page }) => {
    await page.goto('/cat-care/sample-article');
    
    // 检查文章特定的结构化数据
    const articleSchema = await page.$eval(
      'script[type="application/ld+json"]',
      el => JSON.parse(el.textContent)
    );
    expect(articleSchema['@type']).toBe('Article');
    expect(articleSchema.headline).toBeTruthy();
    expect(articleSchema.author).toBeTruthy();
    expect(articleSchema.datePublished).toBeTruthy();
  });
  
  test('Performance metrics', async ({ page }) => {
    await page.goto('/');
    
    // 获取性能指标
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lcp = entries.find(entry => entry.name === 'largest-contentful-paint');
          const fid = entries.find(entry => entry.name === 'first-input');
          const cls = entries.find(entry => entry.name === 'layout-shift');
          
          resolve({
            lcp: lcp?.startTime,
            fid: fid?.processingStart - fid?.startTime,
            cls: cls?.value
          });
        }).observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
      });
    });
    
    // 验证Core Web Vitals
    expect(metrics.lcp).toBeLessThan(2500);
    if (metrics.fid) expect(metrics.fid).toBeLessThan(100);
    if (metrics.cls) expect(metrics.cls).toBeLessThan(0.1);
  });
});
```

### 9.2 SEO检查清单

#### 部署前SEO检查
```typescript
// scripts/seo-checklist.ts
interface SEOCheckResult {
  passed: boolean;
  message: string;
}

async function runSEOChecklist(siteUrl: string): Promise<void> {
  const checks: Array<() => Promise<SEOCheckResult>> = [
    // robots.txt检查
    async () => {
      const response = await fetch(`${siteUrl}/robots.txt`);
      return {
        passed: response.ok,
        message: 'robots.txt is accessible'
      };
    },
    
    // Sitemap检查
    async () => {
      const response = await fetch(`${siteUrl}/sitemap-index.xml`);
      return {
        passed: response.ok,
        message: 'XML sitemap is accessible'
      };
    },
    
    // HTTPS检查
    async () => {
      return {
        passed: siteUrl.startsWith('https://'),
        message: 'Site uses HTTPS'
      };
    },
    
    // 页面速度检查
    async () => {
      const pagespeedUrl = `https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url=${siteUrl}`;
      const response = await fetch(pagespeedUrl);
      const data = await response.json();
      const score = data.lighthouseResult.categories.performance.score;
      
      return {
        passed: score >= 0.9,
        message: `PageSpeed score: ${score * 100}/100`
      };
    }
  ];
  
  console.log('Running SEO Checklist...\n');
  
  for (const check of checks) {
    const result = await check();
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.message}`);
  }
}

// 运行检查
runSEOChecklist('https://example.com');
```

## 10. SEO维护和持续优化

### 10.1 内容更新策略

#### 内容新鲜度管理
```typescript
// utils/seo/contentFreshness.ts
interface ContentFreshnessReport {
  needsUpdate: Article[];
  recentlyUpdated: Article[];
  suggestions: string[];
}

export async function analyzeContentFreshness(
  articles: Article[]
): Promise<ContentFreshnessReport> {
  const now = new Date();
  const needsUpdate: Article[] = [];
  const recentlyUpdated: Article[] = [];
  const suggestions: string[] = [];
  
  articles.forEach(article => {
    const lastUpdate = new Date(article.updated_at);
    const daysSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysSinceUpdate > 365) {
      needsUpdate.push(article);
      suggestions.push(`Update "${article.title}" - last updated ${Math.floor(daysSinceUpdate)} days ago`);
    } else if (daysSinceUpdate < 30) {
      recentlyUpdated.push(article);
    }
  });
  
  // 内容更新建议
  if (needsUpdate.length > 10) {
    suggestions.unshift('High priority: Update outdated content to maintain rankings');
  }
  
  const updateRate = (recentlyUpdated.length / articles.length) * 100;
  if (updateRate < 10) {
    suggestions.push(`Low update rate (${updateRate.toFixed(1)}%) - aim for 10-20% monthly`);
  }
  
  return {
    needsUpdate,
    recentlyUpdated,
    suggestions
  };
}
```

### 10.2 排名监控

#### SERP位置跟踪
```typescript
// utils/seo/rankTracker.ts
interface RankingData {
  keyword: string;
  position: number;
  url: string;
  change: number;
  searchVolume: number;
}

export class RankTracker {
  async trackKeywords(
    domain: string,
    keywords: string[],
    language: string
  ): Promise<RankingData[]> {
    const rankings: RankingData[] = [];
    
    for (const keyword of keywords) {
      const position = await this.checkPosition(domain, keyword, language);
      const previousPosition = await this.getPreviousPosition(keyword, language);
      
      rankings.push({
        keyword,
        position,
        url: await this.getRankingUrl(domain, keyword, language),
        change: previousPosition - position,
        searchVolume: await this.getSearchVolume(keyword, language)
      });
    }
    
    return rankings;
  }
  
  private async checkPosition(
    domain: string,
    keyword: string,
    language: string
  ): Promise<number> {
    // 实现SERP检查逻辑
    // 注意：实际实现需要使用合适的API或服务
    return 0;
  }
  
  generateReport(rankings: RankingData[]): string {
    const improved = rankings.filter(r => r.change > 0).length;
    const declined = rankings.filter(r => r.change < 0).length;
    const stable = rankings.filter(r => r.change === 0).length;
    
    const avgPosition = rankings.reduce((sum, r) => sum + r.position, 0) / rankings.length;
    
    return `
# SEO Ranking Report

## Summary
- Average Position: ${avgPosition.toFixed(1)}
- Improved: ${improved} keywords ↑
- Declined: ${declined} keywords ↓
- Stable: ${stable} keywords →

## Top Performers
${rankings
  .filter(r => r.position <= 10)
  .sort((a, b) => a.position - b.position)
  .map(r => `- "${r.keyword}": Position ${r.position} (${r.change > 0 ? '+' : ''}${r.change})`)
  .join('\n')}

## Needs Attention
${rankings
  .filter(r => r.position > 20 && r.searchVolume > 100)
  .map(r => `- "${r.keyword}": Position ${r.position} (Volume: ${r.searchVolume})`)
  .join('\n')}
    `;
  }
}
```

## 11. 总结和最佳实践

### 11.1 SEO实施优先级

1. **高优先级（必须完成）**
   - 正确的URL结构和规范化
   - 完整的元数据（标题、描述）
   - 移动友好性和响应式设计
   - Core Web Vitals优化
   - XML网站地图
   - 结构化数据（Article、Organization）

2. **中优先级（强烈建议）**
   - 内部链接优化
   - 图片优化和ALT标签
   - 内容质量和关键词优化
   - 页面加载速度优化
   - Hreflang标签实现
   - 面包屑导航

3. **低优先级（锦上添花）**
   - FAQ结构化数据
   - 语音搜索优化
   - 特色片段优化
   - AMP页面（可选）
   - Web Stories（可选）

### 11.2 持续优化流程

1. **每日任务**
   - 监控Core Web Vitals
   - 检查爬虫错误
   - 响应用户评论

2. **每周任务**
   - 发布新内容
   - 更新旧内容
   - 检查排名变化
   - 分析流量数据

3. **每月任务**
   - 全站SEO审计
   - 竞争对手分析
   - 内容差距分析
   - 技术SEO检查

4. **每季度任务**
   - 策略评估和调整
   - 大规模内容更新
   - 网站架构优化
   - 性能基准测试

### 11.3 成功指标

- **技术指标**
  - Core Web Vitals达标率 > 90%
  - 页面加载时间 < 3秒
  - 移动友好性得分 100/100
  - 结构化数据无错误

- **内容指标**
  - 平均停留时间 > 2分钟
  - 跳出率 < 50%
  - 页面浏览量稳定增长
  - 用户参与度提升

- **排名指标**
  - 目标关键词前10名占比 > 30%
  - 长尾关键词覆盖率 > 70%
  - 品牌搜索量增长
  - 特色片段获取率提升

通过严格遵循本SEO优化实施指南，多语言宠物博客站群系统将能够在Google搜索结果中获得优异的排名，吸引大量有价值的自然流量，最终实现商业目标。