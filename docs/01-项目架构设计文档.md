# 多语言宠物博客站群系统 - 项目架构设计文档

## 1. 项目概述

### 1.1 项目背景
本项目是一个专业的多语言宠物博客站群系统，专注于猫狗相关内容的知识分享。系统采用"一语言一模板一域名"的创新架构设计，摒弃传统的i18n国际化方案，确保每个语言版本都是完全独立的前端应用。

### 1.2 核心特点
- **独立站点架构**：每个语言版本是独立的网站，拥有独立的域名和前端模板
- **SEO优先设计**：严格遵循Google SEO最佳实践，确保搜索引擎友好
- **统一内容管理**：通过后台统一管理所有语言版本的内容
- **AI辅助翻译**：集成AI翻译服务，提高内容生产效率
- **高性能架构**：采用静态站点生成技术，确保极速加载

### 1.3 目标市场
初期覆盖三个主要市场：
- 美国（英语）
- 德国（德语）
- 俄罗斯（俄语）

## 2. 技术架构

### 2.1 技术栈选择

#### 前端技术栈
- **框架**: Astro 4.x
- **UI组件**: 原生Web Components + Tailwind CSS
- **构建工具**: Vite
- **SEO工具**: @astrojs/sitemap, astro-seo
- **图片优化**: @astrojs/image
- **客户端交互**: Alpine.js（轻量级交互）

#### 后端技术栈
- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **ORM**: TypeORM 0.3.x
- **认证**: JWT (jsonwebtoken)
- **验证**: class-validator
- **API文档**: @nestjs/swagger
- **日志**: Winston
- **缓存**: Redis

#### 数据库
- **主数据库**: MySQL 9.0.1
- **缓存数据库**: Redis 7.x

#### 部署环境
- **操作系统**: Ubuntu 22.04 LTS
- **Web服务器**: Nginx 1.24.x
- **进程管理**: PM2
- **容器化**: Docker 24.x
- **面板**: 宝塔面板

### 2.2 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户访问层                             │
├─────────────────────┬─────────────────────┬─────────────────┤
│   example.com       │   example.de        │   example.ru    │
│   (英语站点)        │   (德语站点)        │   (俄语站点)    │
└─────────────────────┴─────────────────────┴─────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Nginx (反向代理层)                        │
│  - 域名路由                                                  │
│  - SSL终止                                                   │
│  - 静态文件服务                                              │
│  - 负载均衡                                                  │
└─────────────────────────────────────────────────────────────┘
        │                     │                     │
        ▼                     ▼                     ▼
┌───────────────┐   ┌───────────────┐   ┌───────────────┐
│ Astro Site EN │   │ Astro Site DE │   │ Astro Site RU │
│  (静态站点)   │   │  (静态站点)   │   │  (静态站点)   │
└───────────────┘   └───────────────┘   └───────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    NestJS API Server                         │
│  - RESTful API                                               │
│  - 认证授权                                                  │
│  - 业务逻辑                                                  │
│  - AI翻译集成                                                │
└─────────────────────────────────────────────────────────────┘
        │                                         │
        ▼                                         ▼
┌───────────────┐                       ┌───────────────┐
│   MySQL 9.0.1 │                       │   Redis 7.x   │
│  (主数据库)   │                       │   (缓存层)    │
└───────────────┘                       └───────────────┘
```

### 2.3 数据流架构

```
内容创建流程:
管理员 → 后台管理系统 → 创建中文文章 → AI翻译 → 人工校对 → 发布到各语言站点

用户访问流程:
用户 → 域名访问 → Nginx路由 → 静态HTML页面 → 客户端渲染交互组件

评论提交流程:
用户 → 提交评论 → API验证 → 存储到数据库 → 管理员审核 → 显示在页面
```

## 3. 模块设计

### 3.1 前端模块架构

每个语言站点包含以下模块：

```
astro-site-[lang]/
├── src/
│   ├── components/          # 组件目录
│   │   ├── common/         # 通用组件
│   │   ├── layout/         # 布局组件
│   │   ├── seo/           # SEO组件
│   │   └── interactive/    # 交互组件
│   ├── layouts/            # 页面布局
│   ├── pages/              # 页面文件
│   ├── styles/             # 样式文件
│   ├── utils/              # 工具函数
│   └── config/             # 配置文件
├── public/                 # 静态资源
└── astro.config.mjs       # Astro配置
```

### 3.2 后端模块架构

```
nestjs-api/
├── src/
│   ├── modules/
│   │   ├── auth/           # 认证模块
│   │   ├── articles/       # 文章模块
│   │   ├── categories/     # 分类模块
│   │   ├── comments/       # 评论模块
│   │   ├── media/          # 媒体模块
│   │   ├── translation/    # 翻译模块
│   │   ├── sites/          # 站点配置模块
│   │   └── admin/          # 管理模块
│   ├── common/             # 公共模块
│   │   ├── decorators/     # 装饰器
│   │   ├── filters/        # 异常过滤器
│   │   ├── guards/         # 守卫
│   │   ├── interceptors/   # 拦截器
│   │   └── pipes/          # 管道
│   ├── config/             # 配置
│   └── database/           # 数据库
└── test/                   # 测试文件
```

### 3.3 管理后台架构

```
admin-panel/
├── src/
│   ├── views/              # 页面视图
│   │   ├── dashboard/      # 仪表板
│   │   ├── articles/       # 文章管理
│   │   ├── comments/       # 评论管理
│   │   ├── sites/          # 站点管理
│   │   └── settings/       # 系统设置
│   ├── components/         # UI组件
│   ├── services/           # API服务
│   ├── stores/             # 状态管理
│   └── utils/              # 工具函数
└── public/                 # 静态资源
```

## 4. 核心功能设计

### 4.1 多语言内容管理

#### 内容创建流程
1. 管理员在后台创建中文原始文章
2. 系统自动调用AI翻译API生成其他语言版本
3. 翻译结果保存为草稿状态
4. 管理员进行人工校对和优化
5. 发布到对应的语言站点

#### 内容存储结构
- 原始内容和翻译内容分表存储
- 支持版本控制和历史记录
- 翻译状态跟踪（待翻译、翻译中、待审核、已发布）

### 4.2 域名路由系统

#### 域名识别机制
1. Nginx根据请求域名进行路由
2. 每个域名映射到特定的语言站点
3. 支持www和非www域名
4. 自动跳转到HTTPS

#### 本地开发配置
```nginx
# 本地开发环境nginx配置示例
server {
    server_name dev.example.com;
    location / {
        proxy_pass http://localhost:4321;
    }
}

server {
    server_name dev.example.de;
    location / {
        proxy_pass http://localhost:4322;
    }
}

server {
    server_name dev.example.ru;
    location / {
        proxy_pass http://localhost:4323;
    }
}
```

### 4.3 SEO优化架构

#### 技术SEO实施
1. **URL结构优化**
   - 使用语义化URL
   - 本地化URL slug
   - 扁平化URL结构

2. **页面性能优化**
   - 静态页面生成
   - 资源预加载
   - 图片懒加载
   - CSS/JS优化

3. **结构化数据**
   - Article Schema
   - BreadcrumbList Schema
   - Organization Schema
   - WebSite Schema

4. **元数据管理**
   - 动态生成title和description
   - Open Graph标签
   - Twitter Card标签
   - Canonical URL

### 4.4 评论系统设计

#### 评论功能特性
- 支持多层嵌套（最多3层）
- 邮箱验证
- 反垃圾评论机制
- 管理员审核流程
- 评论通知系统

#### 评论数据流
1. 用户提交评论
2. 前端验证（必填项、格式）
3. API验证（反垃圾、安全检查）
4. 存储到数据库（待审核状态）
5. 管理员审核
6. 更新评论状态
7. 重新生成静态页面

### 4.5 广告集成设计

#### Google AdSense集成
- 每个语言站点独立的AdSense账号
- 响应式广告单元
- 自动广告与手动广告位结合
- 不影响页面加载性能

#### 广告位置策略
- 文章内容中的自然插入
- 侧边栏广告
- 页脚广告
- 不干扰用户阅读体验

## 5. 安全架构

### 5.1 应用安全
- JWT token认证
- 角色权限控制（RBAC）
- API请求限流
- CORS配置
- CSRF保护

### 5.2 数据安全
- SQL注入防护（参数化查询）
- XSS防护（内容过滤）
- 敏感数据加密
- 定期安全审计
- 数据备份策略

### 5.3 服务器安全
- SSH密钥认证
- 防火墙配置
- DDoS防护
- SSL/TLS加密
- 定期系统更新

## 6. 性能优化策略

### 6.1 前端性能
- 静态站点生成（SSG）
- 资源压缩和优化
- CDN加速
- 浏览器缓存策略
- 预连接和预加载

### 6.2 后端性能
- 数据库查询优化
- Redis缓存策略
- API响应压缩
- 连接池管理
- 异步处理

### 6.3 扩展性设计
- 水平扩展支持
- 微服务化准备
- 数据库读写分离
- 缓存集群
- 负载均衡

## 7. 监控和维护

### 7.1 监控体系
- 应用性能监控（APM）
- 错误日志收集
- 用户行为分析
- SEO效果跟踪
- 服务器资源监控

### 7.2 维护策略
- 自动化部署（CI/CD）
- 蓝绿部署
- 数据库迁移管理
- 定期性能优化
- 安全更新

## 8. 项目实施计划

### 8.1 开发阶段
1. **第一阶段**：基础架构搭建（2周）
2. **第二阶段**：核心功能开发（4周）
3. **第三阶段**：前端开发（3周）
4. **第四阶段**：管理后台开发（2周）
5. **第五阶段**：集成测试（1周）
6. **第六阶段**：部署上线（1周）

### 8.2 技术风险管理
- AI翻译质量控制
- 多站点性能优化
- SEO效果监控
- 扩展性验证
- 安全漏洞防护

## 9. 总结

本架构设计充分考虑了项目的特殊需求，采用"一语言一模板一域名"的创新设计，确保了系统的灵活性和扩展性。通过Astro + NestJS的技术组合，既保证了前端的SEO友好性，又确保了后端的健壮性和可维护性。整个架构设计以性能、安全和用户体验为核心，为项目的成功奠定了坚实的基础。