# 多语言宠物博客站群系统 - 数据库设计文档

## 1. 数据库概述

### 1.1 数据库基本信息
- **数据库类型**: MySQL
- **版本**: 9.0.1
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **连接信息**:
  - 服务器: 23.236.69.41
  - 数据库名: bengtai
  - 用户名: bengtai
  - 密码: weizhen258

### 1.2 设计原则
- 使用规范化设计，避免数据冗余
- 支持多语言内容的独立管理
- 考虑查询性能，适当使用索引
- 预留扩展字段，支持未来功能升级
- 使用软删除，保留数据历史

### 1.3 命名规范
- 表名：使用小写字母和下划线，复数形式
- 字段名：使用小写字母和下划线
- 主键：统一使用 `id`
- 外键：使用 `表名_id` 格式
- 时间字段：`created_at`, `updated_at`, `deleted_at`

## 2. 数据表设计

### 2.1 用户管理相关表

#### 2.1.1 users (用户表)
```sql
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `email` VARCHAR(100) NOT NULL COMMENT '邮箱',
  `password` VARCHAR(255) NOT NULL COMMENT '密码（加密）',
  `role` ENUM('super_admin', 'admin', 'editor') NOT NULL DEFAULT 'editor' COMMENT '角色',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间',
  `last_login_ip` VARCHAR(45) NULL COMMENT '最后登录IP',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 2.2 内容管理相关表

#### 2.2.1 categories (分类表)
```sql
CREATE TABLE `categories` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_id` BIGINT UNSIGNED NULL COMMENT '父分类ID',
  `type` ENUM('cat', 'dog') NOT NULL COMMENT '类型：猫或狗',
  `slug` VARCHAR(100) NOT NULL COMMENT 'URL标识',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_type` (`type`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_categories_parent` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';
```

#### 2.2.2 category_translations (分类翻译表)
```sql
CREATE TABLE `category_translations` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` BIGINT UNSIGNED NOT NULL,
  `language` VARCHAR(5) NOT NULL COMMENT '语言代码：zh, en, de, ru',
  `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
  `slug` VARCHAR(100) NOT NULL COMMENT '本地化URL标识',
  `description` TEXT NULL COMMENT '分类描述',
  `meta_title` VARCHAR(255) NULL COMMENT 'SEO标题',
  `meta_description` TEXT NULL COMMENT 'SEO描述',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_language` (`category_id`, `language`),
  UNIQUE KEY `uk_language_slug` (`language`, `slug`),
  KEY `idx_language` (`language`),
  CONSTRAINT `fk_category_translations_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类翻译表';
```

#### 2.2.3 articles (文章表)
```sql
CREATE TABLE `articles` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` BIGINT UNSIGNED NOT NULL COMMENT '分类ID',
  `author_id` BIGINT UNSIGNED NOT NULL COMMENT '作者ID',
  `slug` VARCHAR(200) NOT NULL COMMENT 'URL标识',
  `status` ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `featured_image` VARCHAR(500) NULL COMMENT '特色图片',
  `view_count` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `comment_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论数',
  `published_at` TIMESTAMP NULL COMMENT '发布时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_status` (`status`),
  KEY `idx_published_at` (`published_at`),
  KEY `idx_view_count` (`view_count`),
  CONSTRAINT `fk_articles_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  CONSTRAINT `fk_articles_author` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';
```

#### 2.2.4 article_translations (文章翻译表)
```sql
CREATE TABLE `article_translations` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` BIGINT UNSIGNED NOT NULL,
  `language` VARCHAR(5) NOT NULL COMMENT '语言代码',
  `title` VARCHAR(255) NOT NULL COMMENT '标题',
  `slug` VARCHAR(200) NOT NULL COMMENT '本地化URL标识',
  `content` LONGTEXT NOT NULL COMMENT '内容',
  `excerpt` TEXT NULL COMMENT '摘要',
  `meta_title` VARCHAR(255) NULL COMMENT 'SEO标题',
  `meta_description` TEXT NULL COMMENT 'SEO描述',
  `meta_keywords` VARCHAR(500) NULL COMMENT 'SEO关键词',
  `translation_status` ENUM('pending', 'translating', 'translated', 'reviewed', 'published') NOT NULL DEFAULT 'pending' COMMENT '翻译状态',
  `translator_id` BIGINT UNSIGNED NULL COMMENT '翻译者ID',
  `reviewer_id` BIGINT UNSIGNED NULL COMMENT '审核者ID',
  `translated_at` TIMESTAMP NULL COMMENT '翻译时间',
  `reviewed_at` TIMESTAMP NULL COMMENT '审核时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_article_language` (`article_id`, `language`),
  UNIQUE KEY `uk_language_slug` (`language`, `slug`),
  KEY `idx_language` (`language`),
  KEY `idx_translation_status` (`translation_status`),
  KEY `idx_translator_id` (`translator_id`),
  KEY `idx_reviewer_id` (`reviewer_id`),
  FULLTEXT KEY `ft_title_content` (`title`, `content`),
  CONSTRAINT `fk_article_translations_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_translations_translator` FOREIGN KEY (`translator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_article_translations_reviewer` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章翻译表';
```

#### 2.2.5 tags (标签表)
```sql
CREATE TABLE `tags` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL COMMENT '标签名',
  `slug` VARCHAR(50) NOT NULL COMMENT 'URL标识',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';
```

#### 2.2.6 article_tags (文章标签关联表)
```sql
CREATE TABLE `article_tags` (
  `article_id` BIGINT UNSIGNED NOT NULL,
  `tag_id` BIGINT UNSIGNED NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`article_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`),
  CONSTRAINT `fk_article_tags_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_tags_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章标签关联表';
```

### 2.3 评论管理相关表

#### 2.3.1 comments (评论表)
```sql
CREATE TABLE `comments` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` BIGINT UNSIGNED NOT NULL COMMENT '文章ID',
  `parent_id` BIGINT UNSIGNED NULL COMMENT '父评论ID',
  `language` VARCHAR(5) NOT NULL COMMENT '评论语言',
  `author_name` VARCHAR(50) NOT NULL COMMENT '评论者名称',
  `author_email` VARCHAR(100) NOT NULL COMMENT '评论者邮箱',
  `author_ip` VARCHAR(45) NOT NULL COMMENT '评论者IP',
  `content` TEXT NOT NULL COMMENT '评论内容',
  `status` ENUM('pending', 'approved', 'spam', 'trash') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `is_admin_reply` TINYINT NOT NULL DEFAULT 0 COMMENT '是否管理员回复',
  `approved_by` BIGINT UNSIGNED NULL COMMENT '审核人ID',
  `approved_at` TIMESTAMP NULL COMMENT '审核时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_language` (`language`),
  KEY `idx_status` (`status`),
  KEY `idx_author_email` (`author_email`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_comments_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_parent` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';
```

### 2.4 媒体管理相关表

#### 2.4.1 media (媒体文件表)
```sql
CREATE TABLE `media` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` ENUM('image', 'video', 'document') NOT NULL DEFAULT 'image' COMMENT '类型',
  `filename` VARCHAR(255) NOT NULL COMMENT '文件名',
  `original_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `url` VARCHAR(500) NOT NULL COMMENT '访问URL',
  `mime_type` VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  `size` BIGINT UNSIGNED NOT NULL COMMENT '文件大小（字节）',
  `width` INT UNSIGNED NULL COMMENT '宽度（图片/视频）',
  `height` INT UNSIGNED NULL COMMENT '高度（图片/视频）',
  `alt_text` VARCHAR(255) NULL COMMENT '替代文本',
  `uploaded_by` BIGINT UNSIGNED NOT NULL COMMENT '上传者ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_media_uploaded_by` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='媒体文件表';
```

#### 2.4.2 media_variants (媒体变体表)
```sql
CREATE TABLE `media_variants` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `media_id` BIGINT UNSIGNED NOT NULL COMMENT '原始媒体ID',
  `variant_name` VARCHAR(50) NOT NULL COMMENT '变体名称（如：thumbnail, medium, large）',
  `path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `url` VARCHAR(500) NOT NULL COMMENT '访问URL',
  `width` INT UNSIGNED NOT NULL COMMENT '宽度',
  `height` INT UNSIGNED NOT NULL COMMENT '高度',
  `size` BIGINT UNSIGNED NOT NULL COMMENT '文件大小',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_media_variant` (`media_id`, `variant_name`),
  KEY `idx_variant_name` (`variant_name`),
  CONSTRAINT `fk_media_variants_media` FOREIGN KEY (`media_id`) REFERENCES `media` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='媒体变体表';
```

### 2.5 站点配置相关表

#### 2.5.1 domains (域名表)
```sql
CREATE TABLE `domains` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `domain` VARCHAR(255) NOT NULL COMMENT '域名',
  `language` VARCHAR(5) NOT NULL COMMENT '语言代码',
  `is_primary` TINYINT NOT NULL DEFAULT 0 COMMENT '是否主域名',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `ssl_status` ENUM('none', 'pending', 'active', 'expired') NOT NULL DEFAULT 'none' COMMENT 'SSL状态',
  `ssl_expire_at` DATE NULL COMMENT 'SSL过期时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain` (`domain`),
  KEY `idx_language` (`language`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名表';
```

#### 2.5.2 site_configs (站点配置表)
```sql
CREATE TABLE `site_configs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `language` VARCHAR(5) NOT NULL COMMENT '语言代码',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT NULL COMMENT '配置值',
  `config_type` ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` VARCHAR(500) NULL COMMENT '配置说明',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_language_key` (`language`, `config_key`),
  KEY `idx_language` (`language`),
  KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站点配置表';
```

#### 2.5.3 ad_placements (广告位配置表)
```sql
CREATE TABLE `ad_placements` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `language` VARCHAR(5) NOT NULL COMMENT '语言代码',
  `placement_name` VARCHAR(50) NOT NULL COMMENT '广告位名称',
  `ad_code` TEXT NOT NULL COMMENT '广告代码',
  `position` VARCHAR(50) NOT NULL COMMENT '位置标识',
  `is_active` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_language_placement` (`language`, `placement_name`),
  KEY `idx_language` (`language`),
  KEY `idx_position` (`position`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告位配置表';
```

### 2.6 翻译管理相关表

#### 2.6.1 translation_jobs (翻译任务表)
```sql
CREATE TABLE `translation_jobs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` BIGINT UNSIGNED NOT NULL COMMENT '文章ID',
  `source_language` VARCHAR(5) NOT NULL COMMENT '源语言',
  `target_language` VARCHAR(5) NOT NULL COMMENT '目标语言',
  `status` ENUM('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `ai_model` VARCHAR(50) NULL COMMENT '使用的AI模型',
  `tokens_used` INT UNSIGNED NULL COMMENT '消耗的tokens',
  `error_message` TEXT NULL COMMENT '错误信息',
  `started_at` TIMESTAMP NULL COMMENT '开始时间',
  `completed_at` TIMESTAMP NULL COMMENT '完成时间',
  `created_by` BIGINT UNSIGNED NOT NULL COMMENT '创建者ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_status` (`status`),
  KEY `idx_target_language` (`target_language`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_translation_jobs_article` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_translation_jobs_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译任务表';
```

#### 2.6.2 translation_memories (翻译记忆库)
```sql
CREATE TABLE `translation_memories` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `source_language` VARCHAR(5) NOT NULL COMMENT '源语言',
  `target_language` VARCHAR(5) NOT NULL COMMENT '目标语言',
  `source_text` TEXT NOT NULL COMMENT '源文本',
  `target_text` TEXT NOT NULL COMMENT '译文',
  `context` VARCHAR(255) NULL COMMENT '上下文',
  `usage_count` INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '使用次数',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_languages` (`source_language`, `target_language`),
  KEY `idx_usage_count` (`usage_count`),
  FULLTEXT KEY `ft_source_text` (`source_text`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译记忆库';
```

### 2.7 系统日志相关表

#### 2.7.1 activity_logs (活动日志表)
```sql
CREATE TABLE `activity_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT UNSIGNED NULL COMMENT '用户ID',
  `action` VARCHAR(100) NOT NULL COMMENT '操作动作',
  `entity_type` VARCHAR(50) NULL COMMENT '实体类型',
  `entity_id` BIGINT UNSIGNED NULL COMMENT '实体ID',
  `old_values` JSON NULL COMMENT '旧值',
  `new_values` JSON NULL COMMENT '新值',
  `ip_address` VARCHAR(45) NOT NULL COMMENT 'IP地址',
  `user_agent` TEXT NULL COMMENT '用户代理',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_entity` (`entity_type`, `entity_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_activity_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动日志表';
```

#### 2.7.2 error_logs (错误日志表)
```sql
CREATE TABLE `error_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `level` ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL COMMENT '错误级别',
  `message` TEXT NOT NULL COMMENT '错误信息',
  `context` JSON NULL COMMENT '上下文信息',
  `stack_trace` TEXT NULL COMMENT '堆栈跟踪',
  `user_id` BIGINT UNSIGNED NULL COMMENT '相关用户ID',
  `ip_address` VARCHAR(45) NULL COMMENT 'IP地址',
  `user_agent` TEXT NULL COMMENT '用户代理',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误日志表';
```

### 2.8 统计分析相关表

#### 2.8.1 page_views (页面浏览统计表)
```sql
CREATE TABLE `page_views` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` BIGINT UNSIGNED NULL COMMENT '文章ID',
  `language` VARCHAR(5) NOT NULL COMMENT '语言',
  `page_type` VARCHAR(50) NOT NULL COMMENT '页面类型',
  `page_url` VARCHAR(500) NOT NULL COMMENT '页面URL',
  `referrer` VARCHAR(500) NULL COMMENT '来源',
  `ip_address` VARCHAR(45) NOT NULL COMMENT 'IP地址',
  `user_agent` TEXT NULL COMMENT '用户代理',
  `country_code` VARCHAR(2) NULL COMMENT '国家代码',
  `device_type` ENUM('desktop', 'mobile', 'tablet') NULL COMMENT '设备类型',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_language` (`language`),
  KEY `idx_page_type` (`page_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_country_code` (`country_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面浏览统计表';
```

#### 2.8.2 search_logs (搜索日志表)
```sql
CREATE TABLE `search_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `language` VARCHAR(5) NOT NULL COMMENT '语言',
  `search_query` VARCHAR(255) NOT NULL COMMENT '搜索词',
  `results_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '结果数量',
  `clicked_position` INT UNSIGNED NULL COMMENT '点击位置',
  `ip_address` VARCHAR(45) NOT NULL COMMENT 'IP地址',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_language` (`language`),
  KEY `idx_search_query` (`search_query`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索日志表';
```

## 3. 索引优化策略

### 3.1 索引设计原则
1. 主键自增，使用BIGINT以支持大数据量
2. 外键建立索引，提高JOIN查询性能
3. 常用查询条件建立索引
4. 复合索引按照最左前缀原则设计
5. 全文索引用于文章搜索功能

### 3.2 核心查询优化
1. 文章列表查询：使用复合索引 (language, status, published_at)
2. 分类文章查询：使用索引 (category_id, status, published_at)
3. 评论查询：使用索引 (article_id, status, created_at)
4. 搜索功能：使用全文索引

## 4. 数据迁移计划

### 4.1 初始数据
```sql
-- 插入默认管理员
INSERT INTO `users` (`username`, `email`, `password`, `role`) VALUES
('admin', '<EMAIL>', '$2b$10$...', 'super_admin');

-- 插入默认分类
INSERT INTO `categories` (`type`, `slug`) VALUES
('cat', 'cat-care'),
('cat', 'cat-health'),
('cat', 'cat-behavior'),
('cat', 'cat-breeds'),
('dog', 'dog-care'),
('dog', 'dog-health'),
('dog', 'dog-training'),
('dog', 'dog-breeds');

-- 插入域名配置
INSERT INTO `domains` (`domain`, `language`, `is_primary`) VALUES
('example.com', 'en', 1),
('example.de', 'de', 1),
('example.ru', 'ru', 1);

-- 插入站点基础配置
INSERT INTO `site_configs` (`language`, `config_key`, `config_value`) VALUES
('en', 'site_name', 'Pet Blog'),
('en', 'site_description', 'Your trusted source for pet care'),
('de', 'site_name', 'Haustier Blog'),
('de', 'site_description', 'Ihre vertrauenswürdige Quelle für Haustierpflege'),
('ru', 'site_name', 'Блог о питомцах'),
('ru', 'site_description', 'Ваш надежный источник по уходу за питомцами');
```

### 4.2 数据备份策略
1. 每日自动备份，保留30天
2. 每周全量备份，保留3个月
3. 重要操作前手动备份
4. 备份文件加密存储
5. 定期恢复测试

## 5. 性能优化建议

### 5.1 查询优化
1. 使用预编译语句防止SQL注入
2. 合理使用LIMIT分页
3. 避免SELECT *，只查询需要的字段
4. 使用EXPLAIN分析慢查询
5. 定期更新统计信息

### 5.2 表优化
1. 定期执行OPTIMIZE TABLE
2. 监控表大小，必要时分区
3. 归档历史数据
4. 使用合适的数据类型
5. 避免过度规范化

### 5.3 缓存策略
1. 热门文章缓存到Redis
2. 分类结构缓存
3. 站点配置缓存
4. 查询结果缓存
5. 静态资源CDN缓存

## 6. 安全考虑

### 6.1 数据安全
1. 敏感数据加密存储
2. 使用参数化查询
3. 最小权限原则
4. 定期安全审计
5. 访问日志记录

### 6.2 备份安全
1. 备份文件加密
2. 异地备份存储
3. 备份访问控制
4. 恢复流程测试
5. 备份完整性验证

## 7. 扩展性设计

### 7.1 分区策略
当数据量增长时，考虑对以下表进行分区：
1. page_views - 按月分区
2. activity_logs - 按月分区
3. error_logs - 按月分区
4. comments - 按年分区

### 7.2 读写分离
1. 主库负责写操作
2. 从库负责读操作
3. 关键查询走主库
4. 延迟容忍的查询走从库
5. 自动故障转移

## 8. 维护计划

### 8.1 日常维护
1. 监控慢查询日志
2. 检查表空间使用
3. 更新索引统计
4. 清理过期数据
5. 检查备份状态

### 8.2 定期维护
1. 月度性能分析
2. 季度容量规划
3. 半年度架构评审
4. 年度灾难恢复演练
5. 版本升级评估

## 9. 总结

本数据库设计充分考虑了多语言博客系统的特殊需求，通过合理的表结构设计和索引优化，确保了系统的高性能和可扩展性。设计中特别注重了：

1. **多语言支持**：通过翻译表实现内容的多语言管理
2. **性能优化**：合理的索引设计和查询优化
3. **安全性**：完善的权限控制和数据保护
4. **可扩展性**：预留扩展字段和分区设计
5. **可维护性**：清晰的命名规范和文档说明

该设计能够支持项目的长期发展需求，并为未来的功能扩展预留了充分的空间。