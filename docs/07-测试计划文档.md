# 多语言宠物博客站群系统 - 测试计划文档

## 1. 测试概述

### 1.1 测试目标
- 确保系统功能符合需求规格
- 验证系统性能满足预期指标
- 保证系统安全性和稳定性
- 确认SEO优化效果达标
- 验证多语言独立性和一致性

### 1.2 测试范围
- **功能测试**：所有用户功能和管理功能
- **性能测试**：响应时间、并发处理、资源使用
- **安全测试**：认证授权、数据保护、漏洞扫描
- **兼容性测试**：浏览器、设备、操作系统
- **SEO测试**：搜索引擎优化各项指标
- **国际化测试**：多语言显示和功能

### 1.3 测试策略
- 采用分层测试方法（单元测试、集成测试、系统测试）
- 自动化测试优先，手动测试补充
- 持续集成和持续测试
- 风险驱动的测试优先级

### 1.4 通过标准
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖所有API端点
- 零严重缺陷，中等缺陷 < 5个
- 性能指标全部达标
- SEO审计得分 ≥ 95/100

## 2. 测试环境

### 2.1 测试环境配置
```yaml
backend_test:
  database: MySQL 9.0.1 (测试库)
  cache: Redis 7.x
  framework: NestJS 10.x
  node: 20.x LTS
  
frontend_test:
  framework: Astro 4.x
  browsers:
    - Chrome 120+
    - Firefox 120+
    - Safari 17+
    - Edge 120+
  devices:
    - Desktop (1920x1080, 1366x768)
    - Tablet (768x1024)
    - Mobile (375x667, 414x896)
    
test_data:
  users: 100
  articles: 1000
  comments: 5000
  categories: 20
```

### 2.2 测试工具
- **单元测试**：Jest + Testing Library
- **API测试**：Supertest + Newman
- **E2E测试**：Playwright
- **性能测试**：K6 + Lighthouse
- **安全测试**：OWASP ZAP
- **SEO测试**：Lighthouse + Custom Scripts

## 3. 单元测试计划

### 3.1 后端单元测试

#### 3.1.1 服务层测试
```typescript
// 示例：ArticleService测试
describe('ArticleService', () => {
  let service: ArticleService;
  let repository: Repository<Article>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ArticleService,
        {
          provide: getRepositoryToken(Article),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<ArticleService>(ArticleService);
    repository = module.get<Repository<Article>>(getRepositoryToken(Article));
  });

  describe('findAll', () => {
    it('should return paginated articles', async () => {
      const mockArticles = [/* mock data */];
      jest.spyOn(repository, 'findAndCount').mockResolvedValue([mockArticles, 100]);

      const result = await service.findAll({ page: 1, limit: 20 });
      
      expect(result.data).toHaveLength(20);
      expect(result.total).toBe(100);
      expect(result.page).toBe(1);
    });

    it('should filter by language', async () => {
      const language = 'en';
      await service.findAll({ language });
      
      expect(repository.findAndCount).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({ language })
        })
      );
    });
  });

  describe('create', () => {
    it('should create article with translations', async () => {
      const createDto = {
        categoryId: 1,
        slug: 'test-article',
        translations: {
          zh: { title: '测试文章', content: '内容' }
        }
      };

      const result = await service.create(createDto);
      
      expect(repository.save).toHaveBeenCalled();
      expect(result).toHaveProperty('id');
      expect(result.translations).toHaveLength(1);
    });

    it('should validate unique slug', async () => {
      jest.spyOn(repository, 'findOne').mockResolvedValue({ id: 1 } as Article);
      
      await expect(service.create({ slug: 'existing-slug' }))
        .rejects.toThrow('Slug already exists');
    });
  });
});
```

#### 3.1.2 控制器测试
```typescript
// 示例：AuthController测试
describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            login: jest.fn(),
            refresh: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  describe('login', () => {
    it('should return JWT tokens', async () => {
      const loginDto = { email: '<EMAIL>', password: 'password' };
      const tokens = { 
        access_token: 'jwt_token',
        refresh_token: 'refresh_token'
      };
      
      jest.spyOn(authService, 'login').mockResolvedValue(tokens);
      
      const result = await controller.login(loginDto);
      
      expect(result).toEqual(tokens);
      expect(authService.login).toHaveBeenCalledWith(loginDto);
    });

    it('should handle invalid credentials', async () => {
      jest.spyOn(authService, 'login').mockRejectedValue(
        new UnauthorizedException('Invalid credentials')
      );
      
      await expect(controller.login({ email: '<EMAIL>', password: 'wrong' }))
        .rejects.toThrow(UnauthorizedException);
    });
  });
});
```

### 3.2 前端单元测试

#### 3.2.1 组件测试
```typescript
// 示例：ArticleCard组件测试
import { experimental_AstroContainer as AstroContainer } from 'astro/container';
import ArticleCard from '../ArticleCard.astro';

describe('ArticleCard', () => {
  let container: AstroContainer;

  beforeEach(async () => {
    container = await AstroContainer.create();
  });

  it('renders article information', async () => {
    const article = {
      id: 1,
      title: 'Test Article',
      slug: 'test-article',
      excerpt: 'Test excerpt',
      featured_image: '/test.jpg',
      category: { id: 1, name: 'Test Category', slug: 'test-category' },
      author: { id: 1, username: 'testuser' },
      published_at: '2024-01-01T00:00:00Z',
      view_count: 100,
      comment_count: 5
    };

    const result = await container.renderToString(ArticleCard, {
      props: { article }
    });

    expect(result).toContain(article.title);
    expect(result).toContain(article.excerpt);
    expect(result).toContain('itemtype="https://schema.org/Article"');
  });

  it('applies lazy loading when specified', async () => {
    const result = await container.renderToString(ArticleCard, {
      props: { 
        article: mockArticle,
        lazy: true 
      }
    });

    expect(result).toContain('loading="lazy"');
  });

  it('adds featured class for featured articles', async () => {
    const result = await container.renderToString(ArticleCard, {
      props: { 
        article: mockArticle,
        featured: true 
      }
    });

    expect(result).toContain('article-card--featured');
  });
});
```

#### 3.2.2 工具函数测试
```typescript
// 示例：SEO工具函数测试
import { generateSlug, analyzeKeywords, optimizeMetaDescription } from '../utils/seo';

describe('SEO Utils', () => {
  describe('generateSlug', () => {
    it('generates SEO-friendly URLs', () => {
      expect(generateSlug('How to Care for Your Cat', 'en'))
        .toBe('how-to-care-for-your-cat');
    });

    it('handles German special characters', () => {
      expect(generateSlug('Über die Katzenpflege', 'de'))
        .toBe('ueber-die-katzenpflege');
    });

    it('handles Russian transliteration', () => {
      expect(generateSlug('Уход за кошками', 'ru'))
        .toBe('uhod-za-koshkami');
    });

    it('limits slug length to 60 characters', () => {
      const longTitle = 'This is a very long title that should be truncated to fit within the 60 character limit for SEO purposes';
      const slug = generateSlug(longTitle, 'en');
      expect(slug.length).toBeLessThanOrEqual(60);
    });
  });

  describe('analyzeKeywords', () => {
    it('calculates keyword density correctly', () => {
      const content = 'Cat care is important. Cat health matters. Cat nutrition is key.';
      const analysis = analyzeKeywords(content, ['cat']);
      
      expect(analysis[0].count).toBe(3);
      expect(analysis[0].density).toBeCloseTo(3/11 * 100, 1);
    });

    it('tracks keyword positions', () => {
      const content = 'Cat care is important';
      const analysis = analyzeKeywords(content, ['cat']);
      
      expect(analysis[0].positions).toContain(0);
    });
  });
});
```

## 4. 集成测试计划

### 4.1 API集成测试

#### 4.1.1 认证流程测试
```typescript
describe('Authentication Flow', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    
    userRepository = moduleFixture.get(getRepositoryToken(User));
  });

  afterAll(async () => {
    await app.close();
  });

  it('complete authentication flow', async () => {
    // 1. 创建测试用户
    const hashedPassword = await bcrypt.hash('password123', 10);
    await userRepository.save({
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'editor'
    });

    // 2. 登录
    const loginResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      })
      .expect(200);

    expect(loginResponse.body.data).toHaveProperty('access_token');
    expect(loginResponse.body.data).toHaveProperty('refresh_token');

    const { access_token, refresh_token } = loginResponse.body.data.tokens;

    // 3. 访问受保护资源
    const meResponse = await request(app.getHttpServer())
      .get('/api/v1/auth/me')
      .set('Authorization', `Bearer ${access_token}`)
      .expect(200);

    expect(meResponse.body.data.email).toBe('<EMAIL>');

    // 4. 刷新令牌
    const refreshResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/refresh')
      .send({ refresh_token })
      .expect(200);

    expect(refreshResponse.body.data).toHaveProperty('access_token');

    // 5. 登出
    await request(app.getHttpServer())
      .post('/api/v1/auth/logout')
      .set('Authorization', `Bearer ${access_token}`)
      .expect(200);
  });
});
```

#### 4.1.2 文章管理流程测试
```typescript
describe('Article Management Flow', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    // 初始化应用和获取认证令牌
    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'admin123' });
    
    authToken = response.body.data.tokens.access_token;
  });

  it('complete article lifecycle', async () => {
    // 1. 创建文章
    const createResponse = await request(app.getHttpServer())
      .post('/api/v1/admin/articles')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        category_id: 1,
        slug: 'test-article',
        status: 'draft',
        translations: {
          zh: {
            title: '测试文章',
            content: '<p>这是测试内容</p>',
            excerpt: '测试摘要'
          }
        }
      })
      .expect(201);

    const articleId = createResponse.body.data.id;

    // 2. 触发AI翻译
    const translationResponse = await request(app.getHttpServer())
      .post('/api/v1/admin/translations/jobs')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        article_id: articleId,
        source_language: 'zh',
        target_languages: ['en', 'de', 'ru']
      })
      .expect(201);

    // 3. 等待翻译完成（模拟）
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 4. 更新翻译内容
    await request(app.getHttpServer())
      .put(`/api/v1/admin/articles/${articleId}/translations/en`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        title: 'Test Article',
        content: '<p>This is test content</p>',
        excerpt: 'Test excerpt',
        translation_status: 'reviewed'
      })
      .expect(200);

    // 5. 发布文章
    await request(app.getHttpServer())
      .patch(`/api/v1/admin/articles/${articleId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({ status: 'published' })
      .expect(200);

    // 6. 验证公开访问
    const publicResponse = await request(app.getHttpServer())
      .get(`/api/v1/articles/test-article?language=en`)
      .expect(200);

    expect(publicResponse.body.data.title).toBe('Test Article');
    expect(publicResponse.body.data.status).toBe('published');
  });
});
```

### 4.2 前端集成测试

#### 4.2.1 页面渲染测试
```typescript
import { preview } from 'astro';
import { chromium } from 'playwright';

describe('Frontend Integration', () => {
  let previewServer;
  let browser;
  let page;

  beforeAll(async () => {
    previewServer = await preview({
      root: './frontend-en',
      server: { port: 4321 }
    });
    
    browser = await chromium.launch();
    page = await browser.newPage();
  });

  afterAll(async () => {
    await browser.close();
    await previewServer.stop();
  });

  test('homepage renders with SEO elements', async () => {
    await page.goto('http://localhost:4321');
    
    // 检查标题
    const title = await page.title();
    expect(title).toContain('Pet Blog');
    
    // 检查元描述
    const description = await page.$eval(
      'meta[name="description"]',
      el => el.content
    );
    expect(description).toBeTruthy();
    
    // 检查结构化数据
    const structuredData = await page.$$eval(
      'script[type="application/ld+json"]',
      scripts => scripts.map(s => JSON.parse(s.textContent))
    );
    expect(structuredData).toHaveLength(3); // Article, Organization, WebSite
    
    // 检查文章列表
    const articles = await page.$$('.article-card');
    expect(articles.length).toBeGreaterThan(0);
  });

  test('article page with comments', async () => {
    // 导航到文章页面
    await page.goto('http://localhost:4321/cat-care/sample-article');
    
    // 检查文章内容
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('.article-content')).toBeVisible();
    
    // 测试评论功能
    await page.fill('input[name="author_name"]', 'Test User');
    await page.fill('input[name="author_email"]', '<EMAIL>');
    await page.fill('textarea[name="content"]', 'This is a test comment');
    await page.click('button[type="submit"]');
    
    // 验证成功消息
    await expect(page.locator('.success-message')).toBeVisible();
  });
});
```

## 5. 系统测试计划

### 5.1 端到端测试

#### 5.1.1 用户场景测试
```typescript
import { test, expect } from '@playwright/test';

test.describe('User Journey Tests', () => {
  test('complete user journey from homepage to article comment', async ({ page }) => {
    // 1. 访问首页
    await page.goto('/');
    await expect(page).toHaveTitle(/Pet Blog/);
    
    // 2. 点击分类
    await page.click('a[href="/cat-care"]');
    await expect(page).toHaveURL('/cat-care');
    
    // 3. 选择文章
    const firstArticle = page.locator('.article-card').first();
    const articleTitle = await firstArticle.locator('h3').textContent();
    await firstArticle.click();
    
    // 4. 验证文章页面
    await expect(page.locator('h1')).toHaveText(articleTitle);
    
    // 5. 滚动到评论区
    await page.locator('#comments').scrollIntoViewIfNeeded();
    
    // 6. 提交评论
    await page.fill('input[name="author_name"]', 'John Doe');
    await page.fill('input[name="author_email"]', '<EMAIL>');
    await page.fill('textarea[name="content"]', 'Great article! Very helpful.');
    await page.click('button:has-text("Submit Comment")');
    
    // 7. 验证评论提交成功
    await expect(page.locator('.comment-success')).toBeVisible();
  });

  test('search functionality', async ({ page }) => {
    await page.goto('/');
    
    // 执行搜索
    await page.fill('input[name="search"]', 'cat food');
    await page.press('input[name="search"]', 'Enter');
    
    // 验证搜索结果页面
    await expect(page).toHaveURL(/\/search\?q=cat\+food/);
    await expect(page.locator('.search-results')).toBeVisible();
    
    // 验证搜索结果高亮
    const highlights = await page.locator('mark').count();
    expect(highlights).toBeGreaterThan(0);
  });

  test('multi-language navigation', async ({ page, context }) => {
    // 测试语言切换
    const domains = [
      { url: 'https://example.com', lang: 'en' },
      { url: 'https://example.de', lang: 'de' },
      { url: 'https://example.ru', lang: 'ru' }
    ];
    
    for (const { url, lang } of domains) {
      const newPage = await context.newPage();
      await newPage.goto(url);
      
      // 验证语言特定内容
      const htmlLang = await newPage.getAttribute('html', 'lang');
      expect(htmlLang).toBe(lang);
      
      // 验证hreflang标签
      const hreflangTags = await newPage.$$eval(
        'link[rel="alternate"]',
        links => links.map(l => ({ lang: l.hreflang, href: l.href }))
      );
      expect(hreflangTags).toHaveLength(3);
      
      await newPage.close();
    }
  });
});
```

#### 5.1.2 管理员场景测试
```typescript
test.describe('Admin Journey Tests', () => {
  test.use({
    storageState: 'auth.json' // 预先保存的管理员认证状态
  });

  test('create and publish article workflow', async ({ page }) => {
    // 1. 访问管理后台
    await page.goto('/admin');
    
    // 2. 创建新文章
    await page.click('a:has-text("New Article")');
    
    // 3. 填写文章信息
    await page.fill('input[name="title"]', '如何训练你的猫咪');
    await page.selectOption('select[name="category_id"]', '1');
    
    // 4. 使用富文本编辑器
    const editor = page.frameLocator('.tiptap-editor');
    await editor.locator('.ProseMirror').fill('这是一篇关于猫咪训练的文章...');
    
    // 5. 保存草稿
    await page.click('button:has-text("Save Draft")');
    await expect(page.locator('.toast-success')).toBeVisible();
    
    // 6. 触发AI翻译
    await page.click('button:has-text("Translate")');
    await page.checkBox('input[value="en"]');
    await page.checkBox('input[value="de"]');
    await page.checkBox('input[value="ru"]');
    await page.click('button:has-text("Start Translation")');
    
    // 7. 等待翻译完成
    await page.waitForSelector('.translation-complete', { timeout: 30000 });
    
    // 8. 审核翻译
    await page.click('tab:has-text("English")');
    await page.fill('input[name="title_en"]', 'How to Train Your Cat');
    
    // 9. 发布文章
    await page.click('button:has-text("Publish")');
    await expect(page.locator('.toast-success')).toHaveText(/Article published/);
  });

  test('comment moderation workflow', async ({ page }) => {
    // 1. 访问评论管理
    await page.goto('/admin/comments');
    
    // 2. 筛选待审核评论
    await page.selectOption('select[name="status"]', 'pending');
    
    // 3. 批量选择
    await page.click('input[type="checkbox"][name="select-all"]');
    
    // 4. 批量审核
    await page.click('button:has-text("Bulk Actions")');
    await page.click('button:has-text("Approve Selected")');
    
    // 5. 确认操作
    await page.click('button:has-text("Confirm")');
    await expect(page.locator('.toast-success')).toHaveText(/approved/);
  });
});
```

### 5.2 性能测试

#### 5.2.1 负载测试脚本
```javascript
// k6-load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export const options = {
  stages: [
    { duration: '2m', target: 100 }, // 逐步增加到100个用户
    { duration: '5m', target: 100 }, // 保持100个用户5分钟
    { duration: '2m', target: 200 }, // 增加到200个用户
    { duration: '5m', target: 200 }, // 保持200个用户5分钟
    { duration: '2m', target: 0 },   // 逐步降到0
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95%的请求在500ms内完成
    errors: ['rate<0.1'],             // 错误率低于10%
  },
};

export default function () {
  // 测试首页
  const homepage = http.get('https://example.com');
  check(homepage, {
    'homepage status is 200': (r) => r.status === 200,
    'homepage load time < 500ms': (r) => r.timings.duration < 500,
  });
  errorRate.add(homepage.status !== 200);
  
  sleep(1);
  
  // 测试文章列表API
  const articles = http.get('https://api.example.com/api/v1/articles?language=en');
  check(articles, {
    'articles API status is 200': (r) => r.status === 200,
    'articles API response time < 200ms': (r) => r.timings.duration < 200,
  });
  errorRate.add(articles.status !== 200);
  
  sleep(1);
  
  // 测试文章详情
  if (articles.status === 200) {
    const articleList = JSON.parse(articles.body).data;
    if (articleList.length > 0) {
      const article = articleList[Math.floor(Math.random() * articleList.length)];
      const detail = http.get(`https://example.com/${article.category.slug}/${article.slug}`);
      check(detail, {
        'article detail status is 200': (r) => r.status === 200,
        'article detail load time < 1000ms': (r) => r.timings.duration < 1000,
      });
      errorRate.add(detail.status !== 200);
    }
  }
  
  sleep(2);
}
```

#### 5.2.2 性能监控脚本
```typescript
// lighthouse-performance.ts
import lighthouse from 'lighthouse';
import * as chromeLauncher from 'chrome-launcher';
import { writeFileSync } from 'fs';

async function runLighthouse(url: string, opts = {}) {
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
  opts.port = chrome.port;
  
  const runnerResult = await lighthouse(url, opts);
  
  await chrome.kill();
  
  return runnerResult;
}

async function performanceAudit() {
  const urls = [
    'https://example.com',
    'https://example.com/cat-care',
    'https://example.com/cat-care/sample-article',
    'https://example.de',
    'https://example.ru'
  ];
  
  const results = [];
  
  for (const url of urls) {
    console.log(`Auditing ${url}...`);
    
    const result = await runLighthouse(url, {
      onlyCategories: ['performance', 'accessibility', 'seo'],
    });
    
    const { lhr } = result;
    
    results.push({
      url,
      scores: {
        performance: lhr.categories.performance.score * 100,
        accessibility: lhr.categories.accessibility.score * 100,
        seo: lhr.categories.seo.score * 100,
      },
      metrics: {
        FCP: lhr.audits['first-contentful-paint'].numericValue,
        LCP: lhr.audits['largest-contentful-paint'].numericValue,
        TBT: lhr.audits['total-blocking-time'].numericValue,
        CLS: lhr.audits['cumulative-layout-shift'].numericValue,
        TTI: lhr.audits['interactive'].numericValue,
      }
    });
  }
  
  // 生成报告
  const report = {
    timestamp: new Date().toISOString(),
    results,
    summary: {
      avgPerformance: results.reduce((sum, r) => sum + r.scores.performance, 0) / results.length,
      avgAccessibility: results.reduce((sum, r) => sum + r.scores.accessibility, 0) / results.length,
      avgSEO: results.reduce((sum, r) => sum + r.scores.seo, 0) / results.length,
    }
  };
  
  writeFileSync('performance-report.json', JSON.stringify(report, null, 2));
  
  // 检查是否满足性能要求
  const failedUrls = results.filter(r => 
    r.scores.performance < 90 || 
    r.scores.seo < 95 ||
    r.metrics.LCP > 2500 ||
    r.metrics.CLS > 0.1
  );
  
  if (failedUrls.length > 0) {
    console.error('Performance requirements not met for:', failedUrls.map(u => u.url));
    process.exit(1);
  }
  
  console.log('All performance checks passed!');
}

performanceAudit().catch(console.error);
```

### 5.3 安全测试

#### 5.3.1 安全测试清单
```yaml
authentication_tests:
  - test: "SQL注入测试"
    endpoints:
      - POST /api/v1/auth/login
      - POST /api/v1/auth/register
    payloads:
      - "' OR '1'='1"
      - "admin'--"
      - "1; DROP TABLE users--"
  
  - test: "JWT令牌安全"
    checks:
      - 令牌过期验证
      - 签名算法验证
      - 令牌伪造测试
      - 刷新令牌独立性

authorization_tests:
  - test: "权限提升测试"
    scenarios:
      - 普通用户访问管理接口
      - 编辑者访问超级管理员功能
      - 未认证用户访问受保护资源
  
  - test: "IDOR漏洞测试"
    endpoints:
      - GET /api/v1/admin/articles/:id
      - PUT /api/v1/admin/users/:id
      - DELETE /api/v1/admin/comments/:id

input_validation_tests:
  - test: "XSS防护测试"
    fields:
      - 文章标题
      - 文章内容
      - 评论内容
      - 用户名
    payloads:
      - "<script>alert('XSS')</script>"
      - "<img src=x onerror=alert('XSS')>"
      - "javascript:alert('XSS')"
  
  - test: "文件上传安全"
    checks:
      - 文件类型验证
      - 文件大小限制
      - 文件名净化
      - 恶意文件检测

security_headers_tests:
  required_headers:
    - X-Content-Type-Options: nosniff
    - X-Frame-Options: DENY
    - X-XSS-Protection: 1; mode=block
    - Strict-Transport-Security: max-age=31536000
    - Content-Security-Policy: "default-src 'self'"
```

#### 5.3.2 自动化安全扫描
```typescript
// security-scan.ts
import { ZAPClient } from 'zaproxy';

async function securityScan() {
  const zap = new ZAPClient('localhost', 8080);
  
  // 1. Spider扫描
  await zap.spider.scan('https://example.com');
  await waitForSpiderComplete(zap);
  
  // 2. 主动扫描
  await zap.ascan.scan('https://example.com');
  await waitForScanComplete(zap);
  
  // 3. 获取警报
  const alerts = await zap.core.alerts();
  
  // 4. 生成报告
  const report = {
    high: alerts.filter(a => a.risk === 'High'),
    medium: alerts.filter(a => a.risk === 'Medium'),
    low: alerts.filter(a => a.risk === 'Low'),
    informational: alerts.filter(a => a.risk === 'Informational')
  };
  
  // 5. 检查严重问题
  if (report.high.length > 0) {
    console.error('High risk vulnerabilities found:', report.high);
    process.exit(1);
  }
  
  console.log('Security scan completed successfully');
}
```

## 6. SEO测试计划

### 6.1 技术SEO测试

#### 6.1.1 SEO审计脚本
```typescript
import { chromium } from 'playwright';
import { parseHTML } from 'linkedom';

interface SEOAuditResult {
  url: string;
  issues: Array<{
    type: string;
    severity: 'error' | 'warning' | 'info';
    message: string;
  }>;
  score: number;
}

async function auditSEO(url: string): Promise<SEOAuditResult> {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  await page.goto(url, { waitUntil: 'networkidle' });
  
  const html = await page.content();
  const { document } = parseHTML(html);
  
  const issues = [];
  let score = 100;
  
  // 1. 检查标题
  const title = document.querySelector('title');
  if (!title) {
    issues.push({
      type: 'title',
      severity: 'error',
      message: 'Missing page title'
    });
    score -= 10;
  } else if (title.textContent.length > 60) {
    issues.push({
      type: 'title',
      severity: 'warning',
      message: `Title too long: ${title.textContent.length} characters`
    });
    score -= 5;
  }
  
  // 2. 检查元描述
  const metaDesc = document.querySelector('meta[name="description"]');
  if (!metaDesc) {
    issues.push({
      type: 'meta-description',
      severity: 'error',
      message: 'Missing meta description'
    });
    score -= 10;
  } else if (metaDesc.getAttribute('content').length > 160) {
    issues.push({
      type: 'meta-description',
      severity: 'warning',
      message: `Description too long: ${metaDesc.getAttribute('content').length} characters`
    });
    score -= 5;
  }
  
  // 3. 检查H1标签
  const h1Tags = document.querySelectorAll('h1');
  if (h1Tags.length === 0) {
    issues.push({
      type: 'heading',
      severity: 'error',
      message: 'Missing H1 tag'
    });
    score -= 10;
  } else if (h1Tags.length > 1) {
    issues.push({
      type: 'heading',
      severity: 'warning',
      message: `Multiple H1 tags: ${h1Tags.length}`
    });
    score -= 5;
  }
  
  // 4. 检查图片ALT属性
  const images = document.querySelectorAll('img');
  const imagesWithoutAlt = Array.from(images).filter(img => !img.getAttribute('alt'));
  if (imagesWithoutAlt.length > 0) {
    issues.push({
      type: 'images',
      severity: 'warning',
      message: `${imagesWithoutAlt.length} images missing alt text`
    });
    score -= imagesWithoutAlt.length * 2;
  }
  
  // 5. 检查结构化数据
  const structuredData = document.querySelectorAll('script[type="application/ld+json"]');
  if (structuredData.length === 0) {
    issues.push({
      type: 'structured-data',
      severity: 'warning',
      message: 'No structured data found'
    });
    score -= 5;
  } else {
    // 验证结构化数据
    structuredData.forEach(script => {
      try {
        JSON.parse(script.textContent);
      } catch (e) {
        issues.push({
          type: 'structured-data',
          severity: 'error',
          message: 'Invalid JSON in structured data'
        });
        score -= 10;
      }
    });
  }
  
  // 6. 检查canonical标签
  const canonical = document.querySelector('link[rel="canonical"]');
  if (!canonical) {
    issues.push({
      type: 'canonical',
      severity: 'warning',
      message: 'Missing canonical tag'
    });
    score -= 5;
  }
  
  // 7. 检查hreflang标签
  const hreflangTags = document.querySelectorAll('link[rel="alternate"][hreflang]');
  if (hreflangTags.length < 3) {
    issues.push({
      type: 'hreflang',
      severity: 'warning',
      message: `Only ${hreflangTags.length} hreflang tags found`
    });
    score -= 5;
  }
  
  await browser.close();
  
  return {
    url,
    issues,
    score: Math.max(0, score)
  };
}

// 批量审计
async function batchSEOAudit() {
  const urls = [
    'https://example.com',
    'https://example.com/cat-care',
    'https://example.com/cat-care/sample-article',
    'https://example.de',
    'https://example.ru'
  ];
  
  const results = await Promise.all(urls.map(url => auditSEO(url)));
  
  // 生成报告
  console.log('SEO Audit Report');
  console.log('================');
  
  results.forEach(result => {
    console.log(`\nURL: ${result.url}`);
    console.log(`Score: ${result.score}/100`);
    if (result.issues.length > 0) {
      console.log('Issues:');
      result.issues.forEach(issue => {
        console.log(`  [${issue.severity.toUpperCase()}] ${issue.type}: ${issue.message}`);
      });
    }
  });
  
  const avgScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
  console.log(`\nAverage Score: ${avgScore}/100`);
  
  if (avgScore < 95) {
    console.error('SEO score below threshold!');
    process.exit(1);
  }
}
```

### 6.2 搜索引擎抓取测试

#### 6.2.1 Robots.txt验证
```typescript
async function testRobotsTxt() {
  const urls = [
    'https://example.com/robots.txt',
    'https://example.de/robots.txt',
    'https://example.ru/robots.txt'
  ];
  
  for (const url of urls) {
    const response = await fetch(url);
    const text = await response.text();
    
    // 验证必要的规则
    expect(text).toContain('User-agent: *');
    expect(text).toContain('Sitemap:');
    expect(text).not.toContain('Disallow: /');
    
    // 验证管理路径被屏蔽
    expect(text).toContain('Disallow: /admin');
    expect(text).toContain('Disallow: /api');
  }
}
```

#### 6.2.2 站点地图验证
```typescript
async function testSitemaps() {
  const sitemapIndex = await fetch('https://example.com/sitemap-index.xml');
  const indexXml = await sitemapIndex.text();
  
  // 解析站点地图索引
  const parser = new DOMParser();
  const doc = parser.parseFromString(indexXml, 'text/xml');
  
  const sitemaps = Array.from(doc.querySelectorAll('sitemap loc')).map(
    loc => loc.textContent
  );
  
  expect(sitemaps).toContain('https://example.com/sitemap-articles.xml');
  expect(sitemaps).toContain('https://example.com/sitemap-categories.xml');
  
  // 验证文章站点地图
  const articlesSitemap = await fetch('https://example.com/sitemap-articles.xml');
  const articlesXml = await articlesSitemap.text();
  const articlesDoc = parser.parseFromString(articlesXml, 'text/xml');
  
  const urls = articlesDoc.querySelectorAll('url');
  expect(urls.length).toBeGreaterThan(0);
  
  // 验证每个URL的必要元素
  urls.forEach(url => {
    expect(url.querySelector('loc')).toBeTruthy();
    expect(url.querySelector('lastmod')).toBeTruthy();
    expect(url.querySelector('changefreq')).toBeTruthy();
    expect(url.querySelector('priority')).toBeTruthy();
  });
}
```

## 7. 兼容性测试

### 7.1 浏览器兼容性矩阵

| 浏览器 | 版本 | 桌面端 | 移动端 | 测试优先级 |
|--------|------|--------|--------|------------|
| Chrome | 120+ | ✓ | ✓ | 高 |
| Firefox | 120+ | ✓ | ✓ | 高 |
| Safari | 17+ | ✓ | ✓ | 高 |
| Edge | 120+ | ✓ | - | 中 |
| Samsung Internet | 23+ | - | ✓ | 中 |
| Opera | 106+ | ✓ | ✓ | 低 |

### 7.2 设备测试清单

```yaml
desktop_devices:
  - resolution: 1920x1080
    viewport: 1920x1080
    priority: high
  
  - resolution: 1366x768
    viewport: 1366x768
    priority: high
  
  - resolution: 1440x900
    viewport: 1440x900
    priority: medium
  
  - resolution: 2560x1440
    viewport: 2560x1440
    priority: low

mobile_devices:
  - name: iPhone 14 Pro
    viewport: 393x852
    user_agent: "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X)"
    priority: high
  
  - name: Samsung Galaxy S23
    viewport: 412x915
    user_agent: "Mozilla/5.0 (Linux; Android 13; SM-S911B)"
    priority: high
  
  - name: iPad Pro 12.9
    viewport: 1024x1366
    user_agent: "Mozilla/5.0 (iPad; CPU OS 16_0 like Mac OS X)"
    priority: medium
  
  - name: Pixel 7
    viewport: 412x915
    user_agent: "Mozilla/5.0 (Linux; Android 13; Pixel 7)"
    priority: medium
```

### 7.3 跨浏览器测试脚本

```typescript
import { test, devices } from '@playwright/test';

const browsers = ['chromium', 'firefox', 'webkit'];
const testDevices = [
  { name: 'Desktop Chrome', device: null },
  { name: 'iPhone 14', device: devices['iPhone 14'] },
  { name: 'Galaxy S23', device: devices['Galaxy S23'] },
  { name: 'iPad Pro', device: devices['iPad Pro 11'] }
];

browsers.forEach(browserName => {
  testDevices.forEach(({ name, device }) => {
    test.describe(`${browserName} - ${name}`, () => {
      test.use({
        ...device,
        browserName
      });
      
      test('homepage layout and functionality', async ({ page }) => {
        await page.goto('/');
        
        // 检查响应式布局
        const viewport = page.viewportSize();
        
        if (viewport.width < 768) {
          // 移动端测试
          await expect(page.locator('.mobile-menu-button')).toBeVisible();
          await page.click('.mobile-menu-button');
          await expect(page.locator('.mobile-menu')).toBeVisible();
        } else {
          // 桌面端测试
          await expect(page.locator('.desktop-nav')).toBeVisible();
          await expect(page.locator('.mobile-menu-button')).not.toBeVisible();
        }
        
        // 通用功能测试
        await expect(page.locator('.article-card')).toHaveCount(20);
        await expect(page.locator('footer')).toBeVisible();
      });
      
      test('article page responsive images', async ({ page }) => {
        await page.goto('/cat-care/sample-article');
        
        // 检查图片响应式加载
        const images = page.locator('img');
        const count = await images.count();
        
        for (let i = 0; i < count; i++) {
          const img = images.nth(i);
          await expect(img).toHaveAttribute('loading', 'lazy');
          await expect(img).toHaveAttribute('width');
          await expect(img).toHaveAttribute('height');
        }
      });
    });
  });
});
```

## 8. 测试数据管理

### 8.1 测试数据种子脚本

```typescript
// seed-test-data.ts
import { DataSource } from 'typeorm';
import { faker } from '@faker-js/faker';
import bcrypt from 'bcrypt';

async function seedTestData() {
  const dataSource = new DataSource({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'test',
    password: 'test',
    database: 'pet_blog_test'
  });
  
  await dataSource.initialize();
  
  // 1. 创建测试用户
  const users = [];
  for (let i = 0; i < 10; i++) {
    const user = await dataSource.getRepository('User').save({
      username: faker.internet.userName(),
      email: faker.internet.email(),
      password: await bcrypt.hash('password123', 10),
      role: i === 0 ? 'super_admin' : faker.helpers.arrayElement(['admin', 'editor'])
    });
    users.push(user);
  }
  
  // 2. 创建分类
  const categories = [
    { type: 'cat', slug: 'cat-care', translations: { en: 'Cat Care', de: 'Katzenpflege', ru: 'Уход за кошками' } },
    { type: 'cat', slug: 'cat-health', translations: { en: 'Cat Health', de: 'Katzengesundheit', ru: 'Здоровье кошек' } },
    { type: 'dog', slug: 'dog-care', translations: { en: 'Dog Care', de: 'Hundepflege', ru: 'Уход за собаками' } },
    { type: 'dog', slug: 'dog-health', translations: { en: 'Dog Health', de: 'Hundegesundheit', ru: 'Здоровье собак' } }
  ];
  
  const savedCategories = await Promise.all(
    categories.map(cat => dataSource.getRepository('Category').save(cat))
  );
  
  // 3. 创建文章
  for (let i = 0; i < 100; i++) {
    const article = await dataSource.getRepository('Article').save({
      category_id: faker.helpers.arrayElement(savedCategories).id,
      author_id: faker.helpers.arrayElement(users).id,
      slug: faker.helpers.slugify(faker.lorem.sentence()),
      status: faker.helpers.arrayElement(['draft', 'published', 'archived']),
      featured_image: faker.image.url(),
      view_count: faker.number.int({ min: 0, max: 10000 }),
      published_at: faker.date.past()
    });
    
    // 添加翻译
    const languages = ['en', 'de', 'ru'];
    for (const lang of languages) {
      await dataSource.getRepository('ArticleTranslation').save({
        article_id: article.id,
        language: lang,
        title: faker.lorem.sentence(),
        content: faker.lorem.paragraphs(5, '<p>'),
        excerpt: faker.lorem.paragraph(),
        meta_title: faker.lorem.sentence().substring(0, 60),
        meta_description: faker.lorem.paragraph().substring(0, 160),
        translation_status: 'published'
      });
    }
  }
  
  // 4. 创建评论
  const articles = await dataSource.getRepository('Article').find();
  for (let i = 0; i < 500; i++) {
    await dataSource.getRepository('Comment').save({
      article_id: faker.helpers.arrayElement(articles).id,
      language: faker.helpers.arrayElement(['en', 'de', 'ru']),
      author_name: faker.person.fullName(),
      author_email: faker.internet.email(),
      author_ip: faker.internet.ip(),
      content: faker.lorem.paragraph(),
      status: faker.helpers.arrayElement(['pending', 'approved', 'spam']),
      created_at: faker.date.recent()
    });
  }
  
  console.log('Test data seeded successfully!');
  await dataSource.destroy();
}

seedTestData().catch(console.error);
```

### 8.2 测试数据清理

```typescript
// cleanup-test-data.ts
async function cleanupTestData() {
  const tables = [
    'comments',
    'article_translations',
    'articles',
    'category_translations',
    'categories',
    'users'
  ];
  
  for (const table of tables) {
    await dataSource.query(`TRUNCATE TABLE ${table}`);
  }
  
  console.log('Test data cleaned up!');
}
```

## 9. 测试报告模板

### 9.1 测试总结报告

```markdown
# 测试执行报告

**项目名称**: 多语言宠物博客站群系统
**测试周期**: 2024-01-15 至 2024-01-29
**测试版本**: v1.0.0

## 执行概要

| 测试类型 | 计划用例 | 执行用例 | 通过 | 失败 | 跳过 | 通过率 |
|----------|----------|----------|------|------|------|--------|
| 单元测试 | 150 | 150 | 145 | 5 | 0 | 96.7% |
| 集成测试 | 80 | 80 | 78 | 2 | 0 | 97.5% |
| 系统测试 | 50 | 50 | 49 | 1 | 0 | 98.0% |
| 性能测试 | 20 | 20 | 19 | 1 | 0 | 95.0% |
| 安全测试 | 30 | 30 | 30 | 0 | 0 | 100% |
| SEO测试 | 25 | 25 | 24 | 1 | 0 | 96.0% |
| **总计** | **355** | **355** | **345** | **10** | **0** | **97.2%** |

## 缺陷统计

| 严重级别 | 数量 | 已修复 | 待修复 | 延迟 |
|----------|------|--------|--------|------|
| 严重 | 0 | 0 | 0 | 0 |
| 高 | 2 | 2 | 0 | 0 |
| 中 | 6 | 5 | 1 | 0 |
| 低 | 12 | 10 | 2 | 0 |
| **总计** | **20** | **17** | **3** | **0** |

## 性能测试结果

| 指标 | 目标值 | 实际值 | 状态 |
|------|--------|--------|------|
| 首页加载时间 | < 3s | 2.1s | ✅ |
| API响应时间(P95) | < 500ms | 423ms | ✅ |
| 并发用户数 | 200 | 220 | ✅ |
| Core Web Vitals - LCP | < 2.5s | 2.2s | ✅ |
| Core Web Vitals - FID | < 100ms | 76ms | ✅ |
| Core Web Vitals - CLS | < 0.1 | 0.08 | ✅ |

## SEO测试结果

| 检查项 | 英语站 | 德语站 | 俄语站 |
|--------|--------|--------|--------|
| Lighthouse SEO分数 | 98/100 | 97/100 | 97/100 |
| 移动友好性 | ✅ | ✅ | ✅ |
| 结构化数据 | ✅ | ✅ | ✅ |
| XML站点地图 | ✅ | ✅ | ✅ |
| Hreflang标签 | ✅ | ✅ | ✅ |

## 建议和风险

### 建议
1. 增加文章列表页的缓存时间以提升性能
2. 优化图片加载策略，考虑使用渐进式图片
3. 增加更多的E2E测试场景覆盖边缘情况

### 风险
1. 高并发情况下数据库连接池可能不足
2. AI翻译API的稳定性依赖第三方服务
3. 多语言内容同步可能存在延迟

## 结论

系统整体质量良好，满足发布标准。所有严重和高级别缺陷已修复，剩余的中低级别问题不影响系统正常使用。建议在正式上线前完成剩余缺陷的修复。
```

## 10. 持续测试策略

### 10.1 CI/CD集成

```yaml
# .github/workflows/test.yml
name: Continuous Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Run unit tests
        run: pnpm test:unit
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:9.0.1
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Run integration tests
        run: pnpm test:integration
        env:
          DATABASE_HOST: localhost
          DATABASE_PORT: 3306
          REDIS_HOST: localhost

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Install Playwright
        run: pnpm exec playwright install --with-deps
      
      - name: Run E2E tests
        run: pnpm test:e2e
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: playwright-report/

  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://staging.example.com
            https://staging.example.com/cat-care
          budgetPath: ./.lighthouse/budget.json
          uploadArtifacts: true
```

### 10.2 测试监控仪表板

```typescript
// test-dashboard.ts
import express from 'express';
import { collectTestMetrics } from './metrics';

const app = express();

app.get('/api/test-metrics', async (req, res) => {
  const metrics = await collectTestMetrics();
  
  res.json({
    summary: {
      totalTests: metrics.total,
      passedTests: metrics.passed,
      failedTests: metrics.failed,
      passRate: (metrics.passed / metrics.total * 100).toFixed(2) + '%',
      coverage: metrics.coverage + '%'
    },
    trend: metrics.trend,
    recentFailures: metrics.recentFailures,
    performance: {
      avgTestDuration: metrics.avgDuration + 'ms',
      slowestTests: metrics.slowestTests
    }
  });
});

app.listen(3001, () => {
  console.log('Test dashboard running on http://localhost:3001');
});
```

## 总结

本测试计划涵盖了多语言宠物博客站群系统的全面测试策略，包括：

1. **完整的测试覆盖**：从单元测试到系统测试的全方位覆盖
2. **自动化优先**：大部分测试实现自动化，提高效率
3. **性能和SEO重点**：特别关注系统性能和SEO效果
4. **持续测试**：集成CI/CD实现持续质量保证
5. **多语言验证**：确保各语言版本的独立性和一致性

通过执行本测试计划，可以确保系统的质量、性能和可靠性达到预期标准。