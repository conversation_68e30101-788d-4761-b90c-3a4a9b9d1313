# 多语言宠物博客站群系统 - 前端开发规范文档

## 1. 开发规范概述

### 1.1 核心原则
- **SEO优先**：所有决策都必须优先考虑SEO影响
- **性能至上**：确保页面加载速度和Core Web Vitals指标
- **语言独立**：每个语言版本是完全独立的Astro项目
- **组件复用**：通过npm包共享通用组件
- **响应式设计**：移动优先，适配所有设备

### 1.2 技术栈规范
```json
{
  "astro": "^4.0.0",
  "@astrojs/tailwind": "^5.0.0",
  "@astrojs/image": "^0.18.0",
  "@astrojs/sitemap": "^3.0.0",
  "astro-seo": "^0.8.0",
  "tailwindcss": "^3.4.0",
  "alpinejs": "^3.13.0",
  "typescript": "^5.3.0"
}
```

## 2. 项目结构规范

### 2.1 基础目录结构
```
pet-blog-[language]/
├── src/
│   ├── components/          # 组件目录
│   │   ├── common/         # 通用组件
│   │   │   ├── Header.astro
│   │   │   ├── Footer.astro
│   │   │   ├── Navigation.astro
│   │   │   └── Logo.astro
│   │   ├── layout/         # 布局组件
│   │   │   ├── BaseLayout.astro
│   │   │   ├── ArticleLayout.astro
│   │   │   └── PageLayout.astro
│   │   ├── seo/           # SEO组件
│   │   │   ├── MetaTags.astro
│   │   │   ├── StructuredData.astro
│   │   │   └── Breadcrumbs.astro
│   │   ├── article/       # 文章相关组件
│   │   │   ├── ArticleCard.astro
│   │   │   ├── ArticleList.astro
│   │   │   ├── ArticleContent.astro
│   │   │   └── RelatedArticles.astro
│   │   ├── interactive/   # 交互组件
│   │   │   ├── CommentForm.astro
│   │   │   ├── CommentList.astro
│   │   │   ├── SearchBox.astro
│   │   │   └── ShareButtons.astro
│   │   └── ads/          # 广告组件
│   │       ├── AdBanner.astro
│   │       └── AdSidebar.astro
│   ├── layouts/           # 页面布局
│   │   └── Layout.astro
│   ├── pages/            # 页面文件
│   │   ├── index.astro   # 首页
│   │   ├── [category]/   # 分类页面
│   │   │   └── [...slug].astro
│   │   ├── search.astro  # 搜索页
│   │   ├── about.astro   # 关于页面
│   │   ├── contact.astro # 联系页面
│   │   └── privacy.astro # 隐私政策
│   ├── styles/           # 样式文件
│   │   ├── global.css
│   │   └── tailwind.css
│   ├── utils/           # 工具函数
│   │   ├── api.ts      # API调用
│   │   ├── seo.ts      # SEO工具
│   │   └── helpers.ts  # 辅助函数
│   ├── config/          # 配置文件
│   │   ├── site.ts     # 站点配置
│   │   └── language.ts # 语言配置
│   └── types/          # TypeScript类型
│       ├── api.ts
│       └── content.ts
├── public/             # 静态资源
│   ├── fonts/
│   ├── images/
│   └── robots.txt
├── astro.config.mjs   # Astro配置
├── tailwind.config.js # Tailwind配置
├── tsconfig.json      # TypeScript配置
└── package.json       # 项目配置
```

### 2.2 共享组件库结构
```
pet-blog-shared/
├── src/
│   ├── components/     # 共享组件
│   ├── utils/         # 共享工具
│   └── types/         # 共享类型
├── package.json
└── tsconfig.json
```

## 3. Astro配置规范

### 3.1 基础配置 (astro.config.mjs)
```javascript
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import image from '@astrojs/image';
import sitemap from '@astrojs/sitemap';
import compress from 'astro-compress';

// 根据语言动态配置
const LANGUAGE = process.env.SITE_LANGUAGE || 'en';
const SITE_URL = {
  'en': 'https://example.com',
  'de': 'https://example.de',
  'ru': 'https://example.ru'
}[LANGUAGE];

export default defineConfig({
  site: SITE_URL,
  output: 'static',
  integrations: [
    tailwind({
      config: { applyBaseStyles: false }
    }),
    image({
      serviceEntryPoint: '@astrojs/image/sharp'
    }),
    sitemap({
      customPages: [],
      i18n: {
        defaultLocale: LANGUAGE,
        locales: {
          [LANGUAGE]: LANGUAGE
        }
      }
    }),
    compress({
      css: true,
      html: true,
      js: true,
      img: false,
      svg: true
    })
  ],
  vite: {
    build: {
      cssCodeSplit: true,
      rollupOptions: {
        output: {
          assetFileNames: 'assets/[name].[hash][extname]',
          chunkFileNames: 'chunks/[name].[hash].js',
          entryFileNames: 'entry/[name].[hash].js'
        }
      }
    },
    ssr: {
      external: ['sharp']
    }
  },
  build: {
    inlineStylesheets: 'auto'
  },
  prefetch: {
    prefetchAll: true,
    defaultStrategy: 'viewport'
  }
});
```

### 3.2 环境变量配置
```bash
# .env
SITE_LANGUAGE=en
API_BASE_URL=https://api.example.com/api/v1
PUBLIC_GA_ID=G-XXXXXXXXXX
PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXX
```

## 4. 组件开发规范

### 4.1 Astro组件规范

#### 基础组件模板
```astro
---
// Component: ArticleCard.astro
export interface Props {
  article: Article;
  lazy?: boolean;
  featured?: boolean;
}

const { article, lazy = false, featured = false } = Astro.props;
const imageLoading = lazy ? 'lazy' : 'eager';
---

<article 
  class={`article-card ${featured ? 'article-card--featured' : ''}`}
  itemscope 
  itemtype="https://schema.org/Article"
>
  <a href={`/${article.category.slug}/${article.slug}`} class="article-card__link">
    <img 
      src={article.featured_image} 
      alt={article.title}
      loading={imageLoading}
      decoding="async"
      class="article-card__image"
      width="400"
      height="300"
      itemprop="image"
    />
    <div class="article-card__content">
      <h3 class="article-card__title" itemprop="headline">
        {article.title}
      </h3>
      <p class="article-card__excerpt" itemprop="description">
        {article.excerpt}
      </p>
      <div class="article-card__meta">
        <span itemprop="author" itemscope itemtype="https://schema.org/Person">
          <span itemprop="name">{article.author.username}</span>
        </span>
        <time datetime={article.published_at} itemprop="datePublished">
          {new Date(article.published_at).toLocaleDateString()}
        </time>
      </div>
    </div>
  </a>
</article>

<style>
  .article-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:scale-105;
  }
  
  .article-card--featured {
    @apply md:col-span-2 lg:col-span-3;
  }
  
  .article-card__link {
    @apply block no-underline;
  }
  
  .article-card__image {
    @apply w-full h-48 object-cover;
  }
  
  .article-card__content {
    @apply p-4;
  }
  
  .article-card__title {
    @apply text-xl font-bold mb-2 text-gray-900;
  }
  
  .article-card__excerpt {
    @apply text-gray-600 mb-4 line-clamp-2;
  }
  
  .article-card__meta {
    @apply flex justify-between text-sm text-gray-500;
  }
</style>
```

### 4.2 SEO组件规范

#### MetaTags组件
```astro
---
// Component: MetaTags.astro
export interface Props {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  canonical?: string;
  type?: 'website' | 'article';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

const {
  title,
  description,
  keywords,
  image,
  canonical,
  type = 'website',
  author,
  publishedTime,
  modifiedTime
} = Astro.props;

const siteConfig = await import('@/config/site');
const fullTitle = `${title} | ${siteConfig.siteName}`;
const fullImage = image || siteConfig.defaultImage;
---

<!-- Primary Meta Tags -->
<title>{fullTitle}</title>
<meta name="title" content={fullTitle} />
<meta name="description" content={description} />
{keywords && <meta name="keywords" content={keywords} />}
<link rel="canonical" href={canonical || Astro.url.href} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={Astro.url.href} />
<meta property="og:title" content={fullTitle} />
<meta property="og:description" content={description} />
<meta property="og:image" content={fullImage} />
<meta property="og:site_name" content={siteConfig.siteName} />

{type === 'article' && (
  <>
    {author && <meta property="article:author" content={author} />}
    {publishedTime && <meta property="article:published_time" content={publishedTime} />}
    {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
  </>
)}

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:url" content={Astro.url.href} />
<meta name="twitter:title" content={fullTitle} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={fullImage} />

<!-- Language Alternates -->
<link rel="alternate" hreflang="en" href="https://example.com{Astro.url.pathname}" />
<link rel="alternate" hreflang="de" href="https://example.de{Astro.url.pathname}" />
<link rel="alternate" hreflang="ru" href="https://example.ru{Astro.url.pathname}" />
```

### 4.3 交互组件规范

#### 评论表单组件（使用Alpine.js）
```astro
---
// Component: CommentForm.astro
export interface Props {
  articleId: number;
  parentId?: number;
  language: string;
}

const { articleId, parentId, language } = Astro.props;
---

<div 
  x-data="commentForm()"
  class="comment-form"
>
  <form @submit.prevent="submitComment" class="space-y-4">
    <div>
      <label for="author_name" class="block text-sm font-medium text-gray-700">
        {language === 'en' ? 'Name' : language === 'de' ? 'Name' : 'Имя'} *
      </label>
      <input 
        type="text" 
        id="author_name"
        x-model="formData.author_name"
        required
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        :class="{'border-red-500': errors.author_name}"
      />
      <p x-show="errors.author_name" class="mt-1 text-sm text-red-600" x-text="errors.author_name"></p>
    </div>
    
    <div>
      <label for="author_email" class="block text-sm font-medium text-gray-700">
        {language === 'en' ? 'Email' : language === 'de' ? 'E-Mail' : 'Email'} *
      </label>
      <input 
        type="email" 
        id="author_email"
        x-model="formData.author_email"
        required
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        :class="{'border-red-500': errors.author_email}"
      />
      <p x-show="errors.author_email" class="mt-1 text-sm text-red-600" x-text="errors.author_email"></p>
    </div>
    
    <div>
      <label for="content" class="block text-sm font-medium text-gray-700">
        {language === 'en' ? 'Comment' : language === 'de' ? 'Kommentar' : 'Комментарий'} *
      </label>
      <textarea 
        id="content"
        x-model="formData.content"
        required
        rows="4"
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        :class="{'border-red-500': errors.content}"
      ></textarea>
      <p x-show="errors.content" class="mt-1 text-sm text-red-600" x-text="errors.content"></p>
    </div>
    
    <div>
      <button 
        type="submit"
        :disabled="submitting"
        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
      >
        <span x-show="!submitting">
          {language === 'en' ? 'Submit Comment' : language === 'de' ? 'Kommentar absenden' : 'Отправить комментарий'}
        </span>
        <span x-show="submitting">
          {language === 'en' ? 'Submitting...' : language === 'de' ? 'Wird gesendet...' : 'Отправка...'}
        </span>
      </button>
    </div>
    
    <div x-show="success" class="rounded-md bg-green-50 p-4">
      <p class="text-sm font-medium text-green-800">
        {language === 'en' ? 'Your comment has been submitted and is awaiting moderation.' : 
         language === 'de' ? 'Ihr Kommentar wurde eingereicht und wartet auf Moderation.' : 
         'Ваш комментарий отправлен и ожидает модерации.'}
      </p>
    </div>
    
    <div x-show="error" class="rounded-md bg-red-50 p-4">
      <p class="text-sm font-medium text-red-800" x-text="error"></p>
    </div>
  </form>
</div>

<script>
function commentForm() {
  return {
    formData: {
      author_name: '',
      author_email: '',
      content: ''
    },
    errors: {},
    submitting: false,
    success: false,
    error: null,
    
    validateForm() {
      this.errors = {};
      
      if (!this.formData.author_name.trim()) {
        this.errors.author_name = 'Name is required';
      }
      
      if (!this.formData.author_email.trim()) {
        this.errors.author_email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.formData.author_email)) {
        this.errors.author_email = 'Invalid email format';
      }
      
      if (!this.formData.content.trim()) {
        this.errors.content = 'Comment is required';
      } else if (this.formData.content.length < 10) {
        this.errors.content = 'Comment must be at least 10 characters';
      }
      
      return Object.keys(this.errors).length === 0;
    },
    
    async submitComment() {
      if (!this.validateForm()) return;
      
      this.submitting = true;
      this.error = null;
      
      try {
        const response = await fetch(`${import.meta.env.PUBLIC_API_URL}/articles/${articleId}/comments`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Language': '${language}'
          },
          body: JSON.stringify({
            ...this.formData,
            parent_id: ${parentId || 'null'},
            language: '${language}'
          })
        });
        
        const data = await response.json();
        
        if (data.success) {
          this.success = true;
          this.formData = {
            author_name: '',
            author_email: '',
            content: ''
          };
          
          // Refresh after 3 seconds
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        } else {
          this.error = data.error.message;
        }
      } catch (err) {
        this.error = 'Failed to submit comment. Please try again.';
      } finally {
        this.submitting = false;
      }
    }
  }
}
</script>
```

## 5. 样式规范

### 5.1 Tailwind配置
```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        secondary: {
          50: '#f8fafc',
          500: '#64748b',
          600: '#475569',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        serif: ['Georgia', 'serif'],
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            color: theme('colors.gray.900'),
            a: {
              color: theme('colors.primary.600'),
              '&:hover': {
                color: theme('colors.primary.700'),
              },
            },
            'h1, h2, h3, h4': {
              fontWeight: '700',
            },
          },
        },
      }),
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/line-clamp'),
  ],
}
```

### 5.2 全局样式
```css
/* src/styles/global.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables */
:root {
  --color-primary: #2563eb;
  --color-secondary: #64748b;
  --max-width: 1280px;
  --content-width: 768px;
}

/* Base Styles */
@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply text-gray-900 bg-white;
  }
  
  /* Typography */
  h1 {
    @apply text-4xl md:text-5xl font-bold mb-6;
  }
  
  h2 {
    @apply text-3xl md:text-4xl font-bold mb-4;
  }
  
  h3 {
    @apply text-2xl md:text-3xl font-semibold mb-3;
  }
  
  /* Links */
  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors;
  }
  
  /* Focus Styles */
  :focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
}

/* Component Styles */
@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .content-container {
    @apply max-w-3xl mx-auto;
  }
  
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }
  
  .btn-primary {
    @apply btn text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-primary-500;
  }
}

/* Utility Classes */
@layer utilities {
  /* Hide scrollbar */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  /* Line Clamp */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Aspect Ratios */
  .aspect-16-9 {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-4-3 {
    aspect-ratio: 4 / 3;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  a {
    @apply text-black underline;
  }
}
```

## 6. 性能优化规范

### 6.1 图片优化
```astro
---
// Component: OptimizedImage.astro
import { Image } from '@astrojs/image/components';

export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  loading?: 'lazy' | 'eager';
  sizes?: string;
  aspectRatio?: string;
}

const { 
  src, 
  alt, 
  width = 800, 
  height = 600, 
  loading = 'lazy',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px',
  aspectRatio = '4:3'
} = Astro.props;
---

<picture>
  <source 
    type="image/webp"
    srcset={`
      ${src}?w=400&format=webp 400w,
      ${src}?w=800&format=webp 800w,
      ${src}?w=1200&format=webp 1200w
    `}
    sizes={sizes}
  />
  <Image 
    src={src}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    aspectRatio={aspectRatio}
    format="jpg"
    quality={85}
  />
</picture>
```

### 6.2 代码分割策略
```javascript
// 按需加载交互组件
const CommentSystem = await import('./CommentSystem.astro');
const SearchWidget = await import('./SearchWidget.astro');

// 条件加载
if (hasComments) {
  const { default: Comments } = await import('./Comments.astro');
}
```

### 6.3 预加载关键资源
```astro
---
// Layout.astro
---
<head>
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://api.example.com">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://www.google-analytics.com">
  
  <!-- Preload critical resources -->
  <link rel="preload" href="/fonts/inter-v12-latin-regular.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="/fonts/inter-v12-latin-700.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- DNS Prefetch for third-party domains -->
  <link rel="dns-prefetch" href="https://pagead2.googlesyndication.com">
</head>
```

## 7. API调用规范

### 7.1 API客户端封装
```typescript
// src/utils/api.ts
interface ApiConfig {
  baseURL: string;
  language: string;
  timeout?: number;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  pagination?: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}

class ApiClient {
  private config: ApiConfig;
  
  constructor(config: ApiConfig) {
    this.config = {
      timeout: 10000,
      ...config
    };
  }
  
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.config.baseURL}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'X-Language': this.config.language,
          ...options.headers
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
      
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }
  
  // Article methods
  async getArticles(params: {
    page?: number;
    per_page?: number;
    category_id?: number;
    search?: string;
  }) {
    const queryString = new URLSearchParams({
      language: this.config.language,
      ...params
    }).toString();
    
    return this.request<Article[]>(`/articles?${queryString}`);
  }
  
  async getArticle(slug: string) {
    return this.request<Article>(
      `/articles/${slug}?language=${this.config.language}`
    );
  }
  
  // Category methods
  async getCategories() {
    return this.request<Category[]>(
      `/categories?language=${this.config.language}`
    );
  }
  
  // Comment methods
  async getComments(articleId: number, page = 1) {
    return this.request<Comment[]>(
      `/articles/${articleId}/comments?page=${page}`
    );
  }
  
  async submitComment(articleId: number, data: CommentInput) {
    return this.request<Comment>(`/articles/${articleId}/comments`, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
  
  // Search methods
  async search(query: string, page = 1) {
    const params = new URLSearchParams({
      q: query,
      language: this.config.language,
      page: page.toString()
    });
    
    return this.request<SearchResults>(`/search?${params}`);
  }
}

// Export configured instance
export const api = new ApiClient({
  baseURL: import.meta.env.PUBLIC_API_URL,
  language: import.meta.env.PUBLIC_SITE_LANGUAGE
});
```

### 7.2 数据获取模式
```astro
---
// pages/[category]/[...slug].astro
import { api } from '@/utils/api';
import ArticleLayout from '@/layouts/ArticleLayout.astro';
import type { Article } from '@/types/content';

// 静态路径生成
export async function getStaticPaths() {
  const { data: articles } = await api.getArticles({ per_page: 1000 });
  
  return articles.map((article) => ({
    params: { 
      category: article.category.slug, 
      slug: article.slug 
    },
    props: { article }
  }));
}

const { article } = Astro.props;

// 获取相关数据
const [commentsRes, relatedRes] = await Promise.all([
  api.getComments(article.id),
  api.getRelatedArticles(article.id)
]);

const comments = commentsRes.data || [];
const relatedArticles = relatedRes.data || [];
---

<ArticleLayout article={article}>
  <!-- Article content -->
</ArticleLayout>
```

## 8. TypeScript类型定义

### 8.1 内容类型
```typescript
// src/types/content.ts
export interface Article {
  id: number;
  slug: string;
  title: string;
  content: string;
  excerpt: string;
  featured_image: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
  category: Category;
  tags: string[];
  author: Author;
  view_count: number;
  comment_count: number;
  published_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  type: 'cat' | 'dog';
  description?: string;
  parent_id?: number;
  article_count: number;
}

export interface Comment {
  id: number;
  author_name: string;
  content: string;
  created_at: string;
  is_admin_reply: boolean;
  replies?: Comment[];
}

export interface Author {
  id: number;
  username: string;
  avatar?: string;
}

export interface SearchResults {
  articles: Article[];
  categories: Category[];
  tags: string[];
  total_results: number;
}
```

### 8.2 配置类型
```typescript
// src/types/config.ts
export interface SiteConfig {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  siteLogo: string;
  defaultImage: string;
  language: string;
  locale: string;
  timeZone: string;
  googleAnalyticsId?: string;
  adsenseClientId?: string;
  socialLinks: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
  };
}

export interface LanguageConfig {
  code: string;
  name: string;
  locale: string;
  dateFormat: string;
  translations: {
    [key: string]: string;
  };
}
```

## 9. 国际化处理

### 9.1 语言配置
```typescript
// src/config/language.ts
export const languages = {
  en: {
    code: 'en',
    name: 'English',
    locale: 'en-US',
    dateFormat: 'MMM DD, YYYY',
    translations: {
      'home': 'Home',
      'about': 'About',
      'contact': 'Contact',
      'privacy': 'Privacy Policy',
      'search': 'Search',
      'categories': 'Categories',
      'recent_posts': 'Recent Posts',
      'read_more': 'Read More',
      'share': 'Share',
      'comments': 'Comments',
      'leave_comment': 'Leave a Comment',
      'related_articles': 'Related Articles',
      'cat_care': 'Cat Care',
      'cat_health': 'Cat Health',
      'cat_behavior': 'Cat Behavior',
      'cat_breeds': 'Cat Breeds',
      'dog_care': 'Dog Care',
      'dog_health': 'Dog Health',
      'dog_training': 'Dog Training',
      'dog_breeds': 'Dog Breeds'
    }
  },
  de: {
    code: 'de',
    name: 'Deutsch',
    locale: 'de-DE',
    dateFormat: 'DD. MMM YYYY',
    translations: {
      'home': 'Startseite',
      'about': 'Über uns',
      'contact': 'Kontakt',
      'privacy': 'Datenschutz',
      'search': 'Suche',
      'categories': 'Kategorien',
      'recent_posts': 'Neueste Beiträge',
      'read_more': 'Weiterlesen',
      'share': 'Teilen',
      'comments': 'Kommentare',
      'leave_comment': 'Kommentar hinterlassen',
      'related_articles': 'Ähnliche Artikel',
      'cat_care': 'Katzenpflege',
      'cat_health': 'Katzengesundheit',
      'cat_behavior': 'Katzenverhalten',
      'cat_breeds': 'Katzenrassen',
      'dog_care': 'Hundepflege',
      'dog_health': 'Hundegesundheit',
      'dog_training': 'Hundetraining',
      'dog_breeds': 'Hunderassen'
    }
  },
  ru: {
    code: 'ru',
    name: 'Русский',
    locale: 'ru-RU',
    dateFormat: 'DD MMM YYYY',
    translations: {
      'home': 'Главная',
      'about': 'О нас',
      'contact': 'Контакты',
      'privacy': 'Политика конфиденциальности',
      'search': 'Поиск',
      'categories': 'Категории',
      'recent_posts': 'Последние статьи',
      'read_more': 'Читать далее',
      'share': 'Поделиться',
      'comments': 'Комментарии',
      'leave_comment': 'Оставить комментарий',
      'related_articles': 'Похожие статьи',
      'cat_care': 'Уход за кошками',
      'cat_health': 'Здоровье кошек',
      'cat_behavior': 'Поведение кошек',
      'cat_breeds': 'Породы кошек',
      'dog_care': 'Уход за собаками',
      'dog_health': 'Здоровье собак',
      'dog_training': 'Дрессировка собак',
      'dog_breeds': 'Породы собак'
    }
  }
};

// Helper function
export function t(key: string, lang: string = 'en'): string {
  return languages[lang]?.translations[key] || key;
}
```

## 10. 构建和部署规范

### 10.1 构建脚本
```json
{
  "scripts": {
    "dev": "astro dev",
    "start": "astro dev",
    "build": "astro build",
    "preview": "astro preview",
    "build:en": "SITE_LANGUAGE=en astro build",
    "build:de": "SITE_LANGUAGE=de astro build",
    "build:ru": "SITE_LANGUAGE=ru astro build",
    "build:all": "npm run build:en && npm run build:de && npm run build:ru",
    "test": "vitest",
    "test:e2e": "playwright test",
    "lint": "eslint src --ext .ts,.tsx,.astro",
    "format": "prettier --write \"src/**/*.{ts,tsx,astro,css}\""
  }
}
```

### 10.2 环境配置
```bash
# .env.production
SITE_LANGUAGE=en
API_BASE_URL=https://api.example.com/api/v1
PUBLIC_GA_ID=G-XXXXXXXXXX
PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXX
PUBLIC_SITE_URL=https://example.com
```

### 10.3 构建优化配置
```javascript
// astro.config.mjs - 生产环境优化
export default defineConfig({
  // ... 其他配置
  vite: {
    build: {
      cssCodeSplit: true,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          manualChunks: {
            'vendor': ['alpinejs'],
            'utils': ['./src/utils/api.ts', './src/utils/helpers.ts']
          }
        }
      }
    }
  },
  experimental: {
    assets: true,
    contentCollections: true
  }
});
```

## 11. 测试规范

### 11.1 组件测试
```typescript
// src/components/__tests__/ArticleCard.test.ts
import { experimental_AstroContainer as AstroContainer } from 'astro/container';
import ArticleCard from '../ArticleCard.astro';

describe('ArticleCard', () => {
  it('renders article information correctly', async () => {
    const container = await AstroContainer.create();
    const result = await container.renderToString(ArticleCard, {
      props: {
        article: {
          id: 1,
          title: 'Test Article',
          slug: 'test-article',
          excerpt: 'Test excerpt',
          featured_image: '/test.jpg',
          category: { id: 1, name: 'Test', slug: 'test' },
          author: { id: 1, username: 'admin' },
          published_at: '2024-01-01'
        }
      }
    });
    
    expect(result).toContain('Test Article');
    expect(result).toContain('Test excerpt');
    expect(result).toContain('itemtype="https://schema.org/Article"');
  });
});
```

### 11.2 E2E测试
```typescript
// tests/e2e/homepage.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should load and display articles', async ({ page }) => {
    await page.goto('/');
    
    // Check page title
    await expect(page).toHaveTitle(/Pet Blog/);
    
    // Check for article cards
    const articles = page.locator('.article-card');
    await expect(articles).toHaveCount(20);
    
    // Check navigation
    const nav = page.locator('nav');
    await expect(nav).toBeVisible();
    
    // Test search functionality
    await page.fill('[data-testid="search-input"]', 'cat care');
    await page.press('[data-testid="search-input"]', 'Enter');
    await expect(page).toHaveURL('/search?q=cat+care');
  });
  
  test('should have proper meta tags', async ({ page }) => {
    await page.goto('/');
    
    // Check meta tags
    const description = await page.getAttribute('meta[name="description"]', 'content');
    expect(description).toBeTruthy();
    
    // Check Open Graph tags
    const ogTitle = await page.getAttribute('meta[property="og:title"]', 'content');
    expect(ogTitle).toBeTruthy();
  });
});
```

## 12. 性能监控

### 12.1 Lighthouse CI配置
```javascript
// lighthouserc.js
module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:4321/',
        'http://localhost:4321/cat-care/sample-article',
        'http://localhost:4321/categories/cat-care'
      ],
      numberOfRuns: 3
    },
    assert: {
      assertions: {
        'categories:performance': ['error', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.95 }],
        'categories:best-practices': ['error', { minScore: 0.95 }],
        'categories:seo': ['error', { minScore: 0.95 }],
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', { maxNumericValue: 300 }]
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
};
```

## 13. 开发工具配置

### 13.1 VS Code配置
```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": [
    "javascript",
    "typescript",
    "astro"
  ],
  "files.associations": {
    "*.astro": "astro"
  },
  "[astro]": {
    "editor.defaultFormatter": "astro-build.astro-vscode"
  },
  "tailwindCSS.includeLanguages": {
    "astro": "html"
  }
}
```

### 13.2 ESLint配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:astro/recommended'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  overrides: [
    {
      files: ['*.astro'],
      parser: 'astro-eslint-parser',
      parserOptions: {
        parser: '@typescript-eslint/parser',
        extraFileExtensions: ['.astro']
      }
    }
  ],
  rules: {
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'no-console': ['warn', { allow: ['warn', 'error'] }]
  }
};
```

## 14. 最佳实践总结

### 14.1 SEO最佳实践
1. **URL结构**：使用语义化、本地化的URL
2. **元数据**：每个页面都有唯一的title和description
3. **结构化数据**：实现完整的Schema.org标记
4. **性能优化**：确保Core Web Vitals达标
5. **移动优先**：响应式设计，移动端性能优化

### 14.2 性能最佳实践
1. **静态生成**：尽可能使用SSG而非SSR
2. **图片优化**：WebP格式、懒加载、响应式图片
3. **代码分割**：按需加载JavaScript
4. **缓存策略**：合理的HTTP缓存头
5. **预加载**：关键资源预加载

### 14.3 开发最佳实践
1. **组件化**：小而专注的组件
2. **类型安全**：充分利用TypeScript
3. **错误处理**：优雅的错误处理和用户反馈
4. **测试覆盖**：单元测试和E2E测试
5. **文档完善**：代码注释和开发文档

## 15. 故障排查

### 15.1 常见问题
1. **构建失败**：检查环境变量、API连接
2. **样式问题**：清除缓存、检查Tailwind配置
3. **性能问题**：使用Lighthouse分析、检查图片大小
4. **SEO问题**：验证结构化数据、检查robots.txt

### 15.2 调试技巧
1. 使用Astro开发服务器的详细日志
2. 浏览器开发者工具网络面板
3. 使用性能分析工具
4. 检查构建输出的HTML

## 总结

本前端开发规范为多语言宠物博客系统提供了全面的开发指导。通过遵循这些规范，可以确保：

1. **SEO友好**：完全符合Google最新的SEO要求
2. **高性能**：优秀的加载速度和用户体验
3. **可维护**：清晰的代码结构和开发规范
4. **可扩展**：易于添加新语言和功能
5. **质量保证**：完善的测试和监控体系

开发团队应该严格遵循这些规范，确保项目的长期成功。